declare const dataListRootPropDefs: {
    orientation: {
        type: "enum";
        className: string;
        values: readonly ["horizontal", "vertical"];
        default: "horizontal";
        responsive: true;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3"];
        default: "2";
        responsive: true;
    };
    trim: {
        className: string;
        type: "enum";
        values: readonly ["normal", "start", "end", "both"];
        responsive: true;
    };
};
declare const dataListItemPropDefs: {
    align: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end", "baseline", "stretch"];
        responsive: true;
    };
};
declare const dataListLabelPropDefs: {
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: ("gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky") | undefined;
    };
    width: {
        type: "string";
        className: string;
        customProperties: "--width"[];
        responsive: true;
    };
    minWidth: {
        type: "string";
        className: string;
        customProperties: "--min-width"[];
        responsive: true;
    };
    maxWidth: {
        type: "string";
        className: string;
        customProperties: "--max-width"[];
        responsive: true;
    };
};
export { dataListRootPropDefs, dataListItemPropDefs, dataListLabelPropDefs };
//# sourceMappingURL=data-list.props.d.ts.map