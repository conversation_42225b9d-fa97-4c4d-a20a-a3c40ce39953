import * as React from 'react';
import { emPropDefs } from './em.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type EmOwnProps = GetPropDefTypes<typeof emPropDefs>;
interface EmProps extends ComponentPropsWithout<'em', RemovedProps>, EmOwnProps {
}
declare const Em: React.ForwardRefExoticComponent<EmProps & React.RefAttributes<HTMLElement>>;
export { Em };
export type { EmProps };
//# sourceMappingURL=em.d.ts.map