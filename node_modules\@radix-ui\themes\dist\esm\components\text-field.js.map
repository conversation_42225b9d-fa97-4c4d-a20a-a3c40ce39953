{"version": 3, "sources": ["../../../src/components/text-field.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRefs } from 'radix-ui/internal';\n\nimport { textFieldRootPropDefs, textFieldSlotPropDefs } from './text-field.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { NotInputTextualAttributes } from '../helpers/input-attributes.js';\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TextFieldRootElement = React.ElementRef<'input'>;\ntype TextFieldRootOwnProps = GetPropDefTypes<typeof textFieldRootPropDefs> & {\n  defaultValue?: string | number;\n  value?: string | number;\n  type?:\n    | 'date'\n    | 'datetime-local'\n    | 'email'\n    | 'hidden'\n    | 'month'\n    | 'number'\n    | 'password'\n    | 'search'\n    | 'tel'\n    | 'text'\n    | 'time'\n    | 'url'\n    | 'week';\n};\ntype TextFieldInputProps = ComponentPropsWithout<\n  'input',\n  NotInputTextualAttributes | 'color' | 'defaultValue' | 'size' | 'type' | 'value'\n>;\ninterface TextFieldRootProps extends TextFieldInputProps, MarginProps, TextFieldRootOwnProps {}\nconst TextFieldRoot = React.forwardRef<TextFieldRootElement, TextFieldRootProps>(\n  (props, forwardedRef) => {\n    const inputRef = React.useRef<HTMLInputElement>(null);\n    const { children, className, color, radius, style, ...inputProps } = extractProps(\n      props,\n      textFieldRootPropDefs,\n      marginPropDefs\n    );\n    return (\n      <div\n        data-accent-color={color}\n        data-radius={radius}\n        style={style}\n        className={classNames('rt-TextFieldRoot', className)}\n        onPointerDown={(event) => {\n          const target = event.target as HTMLElement;\n          if (target.closest('input, button, a')) return;\n\n          const input = inputRef.current;\n          if (!input) return;\n\n          // Same selector as in the CSS to find the right slot\n          const isRightSlot = target.closest(`\n            .rt-TextFieldSlot[data-side='right'],\n            .rt-TextFieldSlot:not([data-side='right']) ~ .rt-TextFieldSlot:not([data-side='left'])\n          `);\n\n          const cursorPosition = isRightSlot ? input.value.length : 0;\n\n          requestAnimationFrame(() => {\n            // Only some input types support this, browsers will throw an error if not supported\n            // See: https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange#:~:text=Note%20that%20according,not%20support%20selection%22.\n            try {\n              input.setSelectionRange(cursorPosition, cursorPosition);\n            } catch {}\n            input.focus();\n          });\n        }}\n      >\n        <input\n          spellCheck=\"false\"\n          {...inputProps}\n          ref={composeRefs(inputRef, forwardedRef)}\n          className=\"rt-reset rt-TextFieldInput\"\n        />\n        {children}\n      </div>\n    );\n  }\n);\nTextFieldRoot.displayName = 'TextField.Root';\n\ntype TextFieldSlotElement = React.ElementRef<'div'>;\ntype TextFieldSlotOwnProps = GetPropDefTypes<typeof textFieldSlotPropDefs>;\ninterface TextFieldSlotProps\n  extends ComponentPropsWithout<'div', RemovedProps>,\n    TextFieldSlotOwnProps {}\nconst TextFieldSlot = React.forwardRef<TextFieldSlotElement, TextFieldSlotProps>(\n  (props, forwardedRef) => {\n    const { className, color, side, ...slotProps } = extractProps(props, textFieldSlotPropDefs);\n    return (\n      <div\n        data-accent-color={color}\n        data-side={side}\n        {...slotProps}\n        ref={forwardedRef}\n        className={classNames('rt-TextFieldSlot', className)}\n      />\n    );\n  }\n);\nTextFieldSlot.displayName = 'TextField.Slot';\n\nexport { TextFieldRoot as Root, TextFieldSlot as Slot };\nexport type { TextFieldRootProps as RootProps, TextFieldSlotProps as SlotProps };\n"], "mappings": "aAEA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,eAAAC,MAAmB,oBAE5B,OAAS,yBAAAC,EAAuB,yBAAAC,MAA6B,wBAC7D,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,kBAAAC,MAAsB,2BA+B/B,MAAMC,EAAgBP,EAAM,WAC1B,CAACQ,EAAOC,IAAiB,CACvB,MAAMC,EAAWV,EAAM,OAAyB,IAAI,EAC9C,CAAE,SAAAW,EAAU,UAAAC,EAAW,MAAAC,EAAO,OAAAC,EAAQ,MAAAC,EAAO,GAAGC,CAAW,EAAIX,EACnEG,EACAL,EACAG,CACF,EACA,OACEN,EAAA,cAAC,OACC,oBAAmBa,EACnB,cAAaC,EACb,MAAOC,EACP,UAAWd,EAAW,mBAAoBW,CAAS,EACnD,cAAgBK,GAAU,CACxB,MAAMC,EAASD,EAAM,OACrB,GAAIC,EAAO,QAAQ,kBAAkB,EAAG,OAExC,MAAMC,EAAQT,EAAS,QACvB,GAAI,CAACS,EAAO,OAQZ,MAAMC,EALcF,EAAO,QAAQ;AAAA;AAAA;AAAA,WAGlC,EAEoCC,EAAM,MAAM,OAAS,EAE1D,sBAAsB,IAAM,CAG1B,GAAI,CACFA,EAAM,kBAAkBC,EAAgBA,CAAc,CACxD,MAAQ,CAAC,CACTD,EAAM,MAAM,CACd,CAAC,CACH,GAEAnB,EAAA,cAAC,SACC,WAAW,QACV,GAAGgB,EACJ,IAAKd,EAAYQ,EAAUD,CAAY,EACvC,UAAU,6BACZ,EACCE,CACH,CAEJ,CACF,EACAJ,EAAc,YAAc,iBAO5B,MAAMc,EAAgBrB,EAAM,WAC1B,CAACQ,EAAOC,IAAiB,CACvB,KAAM,CAAE,UAAAG,EAAW,MAAAC,EAAO,KAAAS,EAAM,GAAGC,CAAU,EAAIlB,EAAaG,EAAOJ,CAAqB,EAC1F,OACEJ,EAAA,cAAC,OACC,oBAAmBa,EACnB,YAAWS,EACV,GAAGC,EACJ,IAAKd,EACL,UAAWR,EAAW,mBAAoBW,CAAS,EACrD,CAEJ,CACF,EACAS,EAAc,YAAc", "names": ["React", "classNames", "composeRefs", "textFieldRootPropDefs", "textFieldSlotPropDefs", "extractProps", "marginPropDefs", "TextFieldRoot", "props", "forwardedRef", "inputRef", "children", "className", "color", "radius", "style", "inputProps", "event", "target", "input", "cursorPosition", "TextFieldSlot", "side", "slotProps"]}