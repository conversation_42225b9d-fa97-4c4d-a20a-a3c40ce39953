import { Location, WeatherData, AQIData, Units } from './weather';

// Weather Data Hook Return Type
export interface UseWeatherDataReturn {
  data: WeatherData | null;
  aqi: AQIData | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

// Geolocation Hook Return Type
export interface UseGeolocationReturn {
  getCurrentLocation: () => Promise<Location>;
  loading: boolean;
  error: string | null;
  supported: boolean;
}

// Favorites Hook Return Type
export interface UseFavoritesReturn {
  favorites: Location[];
  addFavorite: (location: Location) => void;
  removeFavorite: (location: Location) => void;
  toggleFavorite: (location: Location) => void;
  isFavorite: (location: Location) => boolean;
  clearFavorites: () => void;
}

// Local Storage Hook Return Type
export interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: T) => void;
  removeValue: () => void;
}

// Search Hook Return Type
export interface UseSearchReturn {
  query: string;
  setQuery: (query: string) => void;
  results: Location[];
  loading: boolean;
  error: string | null;
  clearResults: () => void;
}

// Units Hook Return Type
export interface UseUnitsReturn {
  units: Units;
  setUnits: (units: Units) => void;
  setTemperatureUnit: (unit: 'celsius' | 'fahrenheit') => void;
  setWindUnit: (unit: 'kmh' | 'mph' | 'ms' | 'kn') => void;
}

// Debounce Hook Return Type
export interface UseDebounceReturn<T> {
  debouncedValue: T;
  isDebouncing: boolean;
}

// Local Storage Key Types
export type LocalStorageKey = 'sel' | 'units' | 'favs' | 'theme';

// Error Types
export interface WeatherError {
  message: string;
  code?: string;
  details?: unknown;
}

// API Response Types
export interface APIResponse<T> {
  data: T;
  success: boolean;
  error?: WeatherError;
}
