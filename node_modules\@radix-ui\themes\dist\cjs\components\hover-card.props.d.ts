import { asChildPropDef } from '../props/as-child.prop.js';
import { heightPropDefs } from '../props/height.props.js';
import { widthPropDefs } from '../props/width.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const hoverCardContentPropDefs: {
    height: {
        type: "string";
        className: string;
        customProperties: "--height"[];
        responsive: true;
    };
    minHeight: {
        type: "string";
        className: string;
        customProperties: "--min-height"[];
        responsive: true;
    };
    maxHeight: {
        type: "string";
        className: string;
        customProperties: "--max-height"[];
        responsive: true;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3"];
        default: "2";
        responsive: true;
    };
    width: {
        type: "string";
        className: string;
        customProperties: "--width"[];
        responsive: true;
    };
    minWidth: {
        type: "string";
        className: string;
        customProperties: "--min-width"[];
        responsive: true;
    };
    maxWidth: {
        default: string;
        type: "string";
        className: string;
        customProperties: "--max-width"[];
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
};
type HoverCardContentOwnProps = GetPropDefTypes<typeof hoverCardContentPropDefs & typeof asChildPropDef & typeof widthPropDefs & typeof heightPropDefs>;
export { hoverCardContentPropDefs };
export type { HoverCardContentOwnProps };
//# sourceMappingURL=hover-card.props.d.ts.map