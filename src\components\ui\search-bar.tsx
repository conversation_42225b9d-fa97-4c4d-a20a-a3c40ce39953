'use client';

import React from 'react';
import { 
  Card, 
  Flex, 
  TextField, 
  IconButton, 
  Tooltip, 
  Grid, 
  Text, 
  Separator, 
  Box 
} from '@radix-ui/themes';
import { MagnifyingGlassIcon, Cross2Icon } from '@radix-ui/react-icons';
import { MapPin, Navigation } from 'lucide-react';
import type { SearchBarProps } from '@/types';

export function SearchBar({
  query,
  onQueryChange,
  results,
  onLocationSelect,
  onClearResults,
  onUseGPS,
  loading = false,
  className = '',
}: SearchBarProps): React.ReactElement {
  const handleClear = () => {
    onQueryChange('');
    onClearResults();
  };

  return (
    <Card size="3" className={className}>
      <Flex align="center" gap="2" wrap="wrap">
        <TextField.Root
          size="3"
          style={{ flex: 1, minWidth: '200px' }}
          value={query}
          onChange={(e) => onQueryChange(e.target.value)}
          placeholder="Search by city or ZIP"
          disabled={loading}
        >
          <TextField.Slot>
            <MagnifyingGlassIcon />
          </TextField.Slot>
          {query && (
            <TextField.Slot side="right">
              <IconButton
                variant="ghost"
                color="gray"
                onClick={handleClear}
                disabled={loading}
                style={{ minHeight: '44px', minWidth: '44px' }}
              >
                <Cross2Icon />
              </IconButton>
            </TextField.Slot>
          )}
        </TextField.Root>

        <Tooltip content="Use current location">
          <IconButton
            onClick={onUseGPS}
            variant="soft"
            disabled={loading}
            style={{ minHeight: '44px', minWidth: '44px' }}
          >
            <Navigation size={16} />
          </IconButton>
        </Tooltip>
      </Flex>

      {results.length > 0 && (
        <Box mt="2">
          <Separator size="4" />
          <Grid columns={{ initial: "1", sm: "2" }} gap="2" mt="2">
            {results.map((result) => (
              <Card 
                key={`${result.id}-${result.name}-${result.latitude}-${result.longitude}`}
                onClick={() => onLocationSelect(result)} 
                className="cursor-pointer hover:bg-gray-50 transition-colors"
              >
                <Flex align="center" gap="2" p="2">
                  <MapPin size={16} className="text-gray-500 flex-shrink-0" />
                  <Flex direction="column" gap="1" style={{ minWidth: 0 }}>
                    <Text weight="medium" truncate>
                      {result.name}
                    </Text>
                    <Text color="gray" size="2" truncate>
                      {[result.admin1, result.country].filter(Boolean).join(", ")}
                    </Text>
                  </Flex>
                </Flex>
              </Card>
            ))}
          </Grid>
        </Box>
      )}
    </Card>
  );
}
