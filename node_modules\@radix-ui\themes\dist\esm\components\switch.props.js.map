{"version": 3, "sources": ["../../../src/components/switch.props.tsx"], "sourcesContent": ["import { colorPropDef } from '../props/color.prop.js';\nimport { highContrastPropDef } from '../props/high-contrast.prop.js';\nimport { radiusPropDef } from '../props/radius.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\nconst variants = ['classic', 'surface', 'soft'] as const;\n\nconst switchPropDefs = {\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },\n  ...colorPropDef,\n  ...highContrastPropDef,\n  ...radiusPropDef,\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  variant: PropDef<(typeof variants)[number]>;\n};\n\nexport { switchPropDefs };\n"], "mappings": "AAAA,OAAS,gBAAAA,MAAoB,yBAC7B,OAAS,uBAAAC,MAA2B,iCACpC,OAAS,iBAAAC,MAAqB,0BAI9B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EACtBC,EAAW,CAAC,UAAW,UAAW,MAAM,EAExCC,EAAiB,CACrB,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQF,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQC,EAAU,QAAS,SAAU,EACvF,GAAGJ,EACH,GAAGC,EACH,GAAGC,CACL", "names": ["colorPropDef", "highContrastPropDef", "radiusPropDef", "sizes", "variants", "switchPropDefs"]}