import * as React from 'react';
import { Context } from 'radix-ui/internal';
import * as CheckboxGroupPrimitive from './checkbox-group.primitive.js';
import { checkboxCardsRootPropDefs } from './checkbox-cards.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type CheckboxCardsRootOwnProps = GetPropDefTypes<typeof checkboxCardsRootPropDefs>;
interface CheckboxCardsRootProps extends ComponentPropsWithout<typeof CheckboxGroupPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>, MarginProps, CheckboxCardsRootOwnProps {
}
declare const CheckboxCardsRoot: React.ForwardRefExoticComponent<CheckboxCardsRootProps & React.RefAttributes<HTMLDivElement>>;
interface CheckboxCardsItemProps extends ComponentPropsWithout<typeof CheckboxGroupPrimitive.Item, RemovedProps>, MarginProps {
}
declare const CheckboxCardsItem: React.ForwardRefExoticComponent<CheckboxCardsItemProps & {
    __scopeCheckboxCards?: Context.Scope;
} & React.RefAttributes<HTMLButtonElement>>;
export { CheckboxCardsRoot as Root, CheckboxCardsItem as Item };
export type { CheckboxCardsRootProps as RootProps, CheckboxCardsItemProps as ItemProps };
//# sourceMappingURL=checkbox-cards.d.ts.map