declare const checkboxCardsRootPropDefs: {
    columns: {
        default: string;
        type: "enum | string";
        className: string;
        customProperties: "--grid-template-columns"[];
        values: readonly ["1", "2", "3", "4", "5", "6", "7", "8", "9"];
        parseValue: (value: string) => string;
        responsive: true;
    };
    gap: {
        default: "4";
        type: "enum | string";
        className: string;
        customProperties: "--gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: ("gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky") | undefined;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3"];
        default: "2";
        responsive: true;
    };
    variant: {
        type: "enum";
        className: string;
        values: readonly ["surface", "classic"];
        default: "surface";
    };
    asChild: {
        type: "boolean";
    };
};
export { checkboxCardsRootPropDefs };
//# sourceMappingURL=checkbox-cards.props.d.ts.map