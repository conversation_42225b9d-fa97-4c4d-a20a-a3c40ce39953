{"version": 3, "sources": ["../../../src/components/text-area.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\n\nimport { textAreaPropDefs } from './text-area.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TextAreaElement = React.ElementRef<'textarea'>;\ntype TextAreaOwnProps = GetPropDefTypes<typeof textAreaPropDefs> & {\n  defaultValue?: string;\n  value?: string;\n};\ninterface TextAreaProps\n  extends ComponentPropsWithout<'textarea', RemovedProps | 'size' | 'value'>,\n    MarginProps,\n    TextAreaOwnProps {}\nconst TextArea = React.forwardRef<TextAreaElement, TextAreaProps>((props, forwardedRef) => {\n  const { className, color, radius, style, ...textAreaProps } = extractProps(\n    props,\n    textAreaPropDefs,\n    marginPropDefs\n  );\n  return (\n    <div\n      data-accent-color={color}\n      data-radius={radius}\n      className={classNames('rt-TextAreaRoot', className)}\n      style={style}\n    >\n      <textarea className=\"rt-reset rt-TextAreaInput\" ref={forwardedRef} {...textAreaProps} />\n    </div>\n  );\n});\nTextArea.displayName = 'TextArea';\n\nexport { TextArea };\nexport type { TextAreaProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aAEvB,OAAS,oBAAAC,MAAwB,uBACjC,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,kBAAAC,MAAsB,2BAe/B,MAAMC,EAAWL,EAAM,WAA2C,CAACM,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAC,EAAW,MAAAC,EAAO,OAAAC,EAAQ,MAAAC,EAAO,GAAGC,CAAc,EAAIT,EAC5DG,EACAJ,EACAE,CACF,EACA,OACEJ,EAAA,cAAC,OACC,oBAAmBS,EACnB,cAAaC,EACb,UAAWT,EAAW,kBAAmBO,CAAS,EAClD,MAAOG,GAEPX,EAAA,cAAC,YAAS,UAAU,4BAA4B,IAAKO,EAAe,GAAGK,EAAe,CACxF,CAEJ,CAAC,EACDP,EAAS,YAAc", "names": ["React", "classNames", "textAreaPropDefs", "extractProps", "marginPropDefs", "TextArea", "props", "forwardedRef", "className", "color", "radius", "style", "textAreaProps"]}