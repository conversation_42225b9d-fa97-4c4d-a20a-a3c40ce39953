import * as React from 'react';
import { tableRootPropDefs, tableRowPropDefs, tableCellPropDefs } from './table.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type TableRootOwnProps = GetPropDefTypes<typeof tableRootPropDefs>;
interface TableRootProps extends ComponentPropsWithout<'div', RemovedProps>, MarginProps, TableRootOwnProps {
}
declare const TableRoot: React.ForwardRefExoticComponent<TableRootProps & React.RefAttributes<HTMLDivElement>>;
interface TableHeaderProps extends ComponentPropsWithout<'thead', RemovedProps> {
}
declare const TableHeader: React.ForwardRefExoticComponent<TableHeaderProps & React.RefAttributes<HTMLTableSectionElement>>;
interface TableBodyProps extends ComponentPropsWithout<'tbody', RemovedProps> {
}
declare const TableBody: React.ForwardRefExoticComponent<TableBodyProps & React.RefAttributes<HTMLTableSectionElement>>;
type TableRowOwnProps = GetPropDefTypes<typeof tableRowPropDefs>;
interface TableRowProps extends ComponentPropsWithout<'tr', RemovedProps>, TableRowOwnProps {
}
declare const TableRow: React.ForwardRefExoticComponent<TableRowProps & React.RefAttributes<HTMLTableRowElement>>;
type TableCellOwnProps = GetPropDefTypes<typeof tableCellPropDefs>;
interface TableCellProps extends ComponentPropsWithout<'td', RemovedProps | 'width'>, TableCellOwnProps {
}
declare const TableCell: React.ForwardRefExoticComponent<TableCellProps & React.RefAttributes<HTMLTableDataCellElement>>;
interface TableColumnHeaderCellProps extends ComponentPropsWithout<'th', RemovedProps>, TableCellOwnProps {
}
declare const TableColumnHeaderCell: React.ForwardRefExoticComponent<TableColumnHeaderCellProps & React.RefAttributes<HTMLTableHeaderCellElement>>;
interface TableRowHeaderCellProps extends ComponentPropsWithout<'th', RemovedProps>, TableCellOwnProps {
}
declare const TableRowHeaderCell: React.ForwardRefExoticComponent<TableRowHeaderCellProps & React.RefAttributes<HTMLTableHeaderCellElement>>;
export { TableRoot as Root, TableHeader as Header, TableBody as Body, TableRow as Row, TableCell as Cell, TableColumnHeaderCell as ColumnHeaderCell, TableRowHeaderCell as RowHeaderCell, };
export type { TableRootProps as RootProps, TableHeaderProps as HeaderProps, TableBodyProps as BodyProps, TableRowProps as RowProps, TableCellProps as CellProps, TableColumnHeaderCellProps as ColumnHeaderCellProps, TableRowHeaderCellProps as RowHeaderCellProps, };
//# sourceMappingURL=table.d.ts.map