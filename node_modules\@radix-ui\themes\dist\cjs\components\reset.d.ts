import * as React from 'react';
import { Slot } from 'radix-ui';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface ResetProps extends ComponentPropsWithout<typeof Slot.Root, RemovedProps> {
}
declare const Reset: React.ForwardRefExoticComponent<ResetProps & React.RefAttributes<HTMLElement>>;
export { Reset };
export type { ResetProps };
//# sourceMappingURL=reset.d.ts.map