'use client';

import React from 'react';
import { Box, Text, Flex, Card } from '@radix-ui/themes';
import { aqiCategory } from '@/lib/openmeteo';
import { Wind } from 'lucide-react';
import type { AirQualityProps } from '@/types';

export function AirQuality({
  aqi,
  className = '',
}: AirQualityProps): React.ReactElement | null {
  // Check if AQI data is available
  if (!aqi?.hourly?.us_aqi || aqi.hourly.us_aqi.length === 0) {
    return null;
  }

  const currentAQI = aqi.hourly.us_aqi[0];
  const category = aqiCategory(currentAQI);

  // Color mapping for AQI categories
  const getAQIColor = (color: string) => {
    switch (color) {
      case 'green': return 'text-green-600 bg-green-50 border-green-200';
      case 'yellow': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'orange': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'red': return 'text-red-600 bg-red-50 border-red-200';
      case 'purple': return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'maroon': return 'text-red-800 bg-red-100 border-red-300';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <Box className={className}>
      <Text weight="medium" mb="2">
        Air Quality
      </Text>
      <Flex mt="2" align="center" gap="2">
        <Card className={`border ${getAQIColor(category.color)}`}>
          <Flex align="center" gap="2" p="3">
            <Wind size={20} />
            <Flex direction="column" gap="1">
              <Text weight="medium">
                US AQI: {currentAQI}
              </Text>
              <Text size="2" color="gray">
                {category.label}
              </Text>
            </Flex>
          </Flex>
        </Card>
      </Flex>
    </Box>
  );
}
