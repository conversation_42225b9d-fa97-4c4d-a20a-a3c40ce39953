import * as React from 'react';
import { textPropDefs } from './text.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
type TextOwnProps = GetPropDefTypes<typeof textPropDefs>;
interface CommonTextProps extends MarginProps, TextOwnProps {
}
type TextSpanProps = {
    as?: 'span';
} & ComponentPropsWithout<'span', RemovedProps>;
type TextDivProps = {
    as: 'div';
} & ComponentPropsWithout<'div', RemovedProps>;
type TextLabelProps = {
    as: 'label';
} & ComponentPropsWithout<'label', RemovedProps>;
type TextPProps = {
    as: 'p';
} & ComponentPropsWithout<'p', RemovedProps>;
type TextProps = CommonTextProps & (TextSpanProps | TextDivProps | TextLabelProps | TextPProps);
declare const Text: React.ForwardRefExoticComponent<TextProps & React.RefAttributes<HTMLSpanElement>>;
export { Text };
export type { TextProps };
//# sourceMappingURL=text.d.ts.map