// Weather icon mapping using Lucide React icons
import React from 'react';
import {
  Sun,
  Moon,
  Cloud,
  CloudRain,
  CloudSnow,
  CloudFog,
  Zap,
  CloudDrizzle,
  Snowflake,
  type LucideIcon,
} from 'lucide-react';

import type { WeatherCode } from '@/types';

// Icon component mapping
const WEATHER_ICONS: Record<string, LucideIcon> = {
  sun: Sun,
  moon: Moon,
  cloud: Cloud,
  'cloud-rain': CloudRain,
  'cloud-drizzle': CloudDrizzle,
  'cloud-snow': CloudSnow,
  snowflake: Snowflake,
  'cloud-fog': CloudFog,
  zap: Zap,
};

// Get icon name based on weather code
export function getWeatherIconName(code: WeatherCode, isDay: number = 1): string {
  // Clear sky
  if ([0, 1].includes(code)) return isDay ? "sun" : "moon";
  // Partly cloudy
  if (code === 2) return isDay ? "sun" : "moon"; // Could use a partly cloudy icon if available
  // Overcast
  if (code === 3) return "cloud";
  // Fog
  if ([45, 48].includes(code)) return "cloud-fog";
  // Drizzle
  if ([51, 53, 55, 56, 57].includes(code)) return "cloud-drizzle";
  // Rain
  if ([61, 63, 65, 66, 67, 80, 81, 82].includes(code)) return "cloud-rain";
  // Snow
  if ([71, 73, 75, 77, 85, 86].includes(code)) return "snowflake";
  // Thunderstorm
  if ([95, 96, 99].includes(code)) return "zap";
  // Default
  return "cloud";
}

// Get icon component
export function getWeatherIcon(iconName: string): LucideIcon {
  return WEATHER_ICONS[iconName] || Cloud;
}

// Weather icon component with proper sizing and accessibility
interface WeatherIconProps {
  code: WeatherCode;
  isDay?: number;
  size?: number;
  className?: string;
}

export function WeatherIcon({ 
  code, 
  isDay = 1, 
  size = 24, 
  className = "" 
}: WeatherIconProps): React.ReactElement {
  const iconName = getWeatherIconName(code, isDay);
  const IconComponent = getWeatherIcon(iconName);
  
  return (
    <IconComponent 
      size={size} 
      className={className}
      aria-label={`Weather: ${iconName.replace('-', ' ')}`}
    />
  );
}

// Large weather icon for current weather display
export function LargeWeatherIcon({ 
  code, 
  isDay = 1, 
  className = "" 
}: Omit<WeatherIconProps, 'size'>): React.ReactElement {
  return (
    <WeatherIcon 
      code={code} 
      isDay={isDay} 
      size={64} 
      className={`text-blue-500 ${className}`}
    />
  );
}

// Small weather icon for forecasts
export function SmallWeatherIcon({ 
  code, 
  isDay = 1, 
  className = "" 
}: Omit<WeatherIconProps, 'size'>): React.ReactElement {
  return (
    <WeatherIcon 
      code={code} 
      isDay={isDay} 
      size={32} 
      className={`text-gray-600 ${className}`}
    />
  );
}
