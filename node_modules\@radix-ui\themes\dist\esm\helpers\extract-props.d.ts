import type * as React from 'react';
import type { PropDef } from '../props/prop-def.js';
type PropDefsWithClassName<T> = T extends Record<string, PropDef> ? {
    [K in keyof T]: T[K] extends {
        className: string;
    } ? K : never;
}[keyof T] : never;
/**
 * Takes props, checks them against prop defs that have a `className` on them,
 * adds necessary CSS classes and inline styles, and returns the props without
 * the corresponding prop defs that were used to formulate the new `className`
 * and `style` values. Also applies prop def defaults to every prop.
 */
declare function extractProps<P extends {
    className?: string;
    style?: React.CSSProperties;
    [key: string]: any;
}, T extends Record<string, PropDef>[]>(props: P, ...propDefs: T): Omit<P & {
    className?: string;
    style?: React.CSSProperties;
}, PropDefsWithClassName<T[number]>>;
export { extractProps };
//# sourceMappingURL=extract-props.d.ts.map