import * as React from 'react';
import { radioPropDefs } from './radio.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout } from '../helpers/component-props.js';
import type { NotInputRadioAttributes } from '../helpers/input-attributes.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type RadioOwnProps = GetPropDefTypes<typeof radioPropDefs> & {
    value: string;
    onValueChange?: (value: string) => void;
};
type RadioInputProps = ComponentPropsWithout<'input', NotInputRadioAttributes | 'color' | 'defaultValue' | 'value'>;
interface RadioProps extends RadioInputProps, MarginProps, RadioOwnProps {
}
declare const Radio: React.ForwardRefExoticComponent<RadioProps & React.RefAttributes<HTMLInputElement>>;
export { Radio };
export type { RadioProps };
//# sourceMappingURL=radio.d.ts.map