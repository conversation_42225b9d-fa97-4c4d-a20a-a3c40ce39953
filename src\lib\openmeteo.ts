// Open-Meteo API helpers with TypeScript support
// Geocoding, Weather, AQI endpoints and utilities

import type {
  Location,
  WeatherData,
  AQIData,
  WeatherFetchOptions,
  GeocodingOptions,
  PostalGeocodingOptions,
  WeatherCode,
  WeatherCodeMapping,
  AQICategory,
  PressureData,
  LocalStorageKey,
} from '@/types';

const BASE_GEO = "https://geocoding-api.open-meteo.com/v1/search";
const BASE_WEATHER = "https://api.open-meteo.com/v1/forecast";
const BASE_AQI = "https://air-quality-api.open-meteo.com/v1/air-quality";

// Geocoding functions
export async function geocodeCity(
  query: string, 
  options: GeocodingOptions = {}
): Promise<Location[]> {
  if (!query) return [];
  
  const { count = 5 } = options;
  const url = new URL(BASE_GEO);
  url.searchParams.set("name", query);
  url.searchParams.set("count", String(count));
  url.searchParams.set("language", "en");
  url.searchParams.set("format", "json");

  const res = await fetch(url.toString(), { cache: "no-store" });
  if (!res.ok) throw new Error("Geocoding failed");
  
  const data = await res.json();
  return data?.results || [];
}

export async function geocodePostal(
  postal: string, 
  country?: string,
  options: PostalGeocodingOptions = {}
): Promise<Location[]> {
  if (!postal) return [];
  
  const { count = 5 } = options;
  const url = new URL(BASE_GEO);
  url.searchParams.set("postal_code", postal);
  if (country) url.searchParams.set("country", country);
  url.searchParams.set("count", String(count));
  url.searchParams.set("language", "en");
  url.searchParams.set("format", "json");
  
  const res = await fetch(url.toString(), { cache: "no-store" });
  if (!res.ok) throw new Error("Postal geocoding failed");
  
  const data = await res.json();
  return data?.results || [];
}

// Weather API functions
export function buildWeatherURL(options: WeatherFetchOptions): string {
  const {
    latitude,
    longitude,
    tempUnit = "celsius",
    windUnit = "kmh",
    hours = 24,
  } = options;

  const url = new URL(BASE_WEATHER);
  url.searchParams.set("latitude", String(latitude));
  url.searchParams.set("longitude", String(longitude));
  url.searchParams.set(
    "current",
    [
      "temperature_2m",
      "apparent_temperature",
      "weather_code",
      "relative_humidity_2m",
      "wind_speed_10m",
      "pressure_msl",
      "is_day",
    ].join(",")
  );
  url.searchParams.set("hourly", ["temperature_2m", "weather_code"].join(","));
  url.searchParams.set(
    "daily",
    [
      "temperature_2m_max",
      "temperature_2m_min",
      "sunrise",
      "sunset",
      "weather_code",
    ].join(",")
  );
  url.searchParams.set("timezone", "auto");
  url.searchParams.set("forecast_days", "7");
  url.searchParams.set("past_hours", "0");
  url.searchParams.set("future_hours", String(hours));
  url.searchParams.set("temperature_unit", tempUnit);
  url.searchParams.set("windspeed_unit", windUnit);
  
  return url.toString();
}

export async function fetchWeather(options: WeatherFetchOptions): Promise<WeatherData> {
  const url = buildWeatherURL(options);
  const res = await fetch(url, { cache: "no-store" });
  if (!res.ok) throw new Error("Weather fetch failed");
  return res.json();
}

// Air Quality API functions
export function buildAQIURL(options: { latitude: number; longitude: number }): string {
  const { latitude, longitude } = options;
  const url = new URL(BASE_AQI);
  url.searchParams.set("latitude", String(latitude));
  url.searchParams.set("longitude", String(longitude));
  url.searchParams.set("hourly", ["pm10", "pm2_5", "us_aqi"].join(","));
  url.searchParams.set("timezone", "auto");
  url.searchParams.set("past_hours", "0");
  url.searchParams.set("forecast_hours", "24");
  return url.toString();
}

export async function fetchAQI(options: { latitude: number; longitude: number }): Promise<AQIData> {
  const res = await fetch(buildAQIURL(options), { cache: "no-store" });
  if (!res.ok) throw new Error("AQI fetch failed");
  return res.json();
}

// Weather code mappings
const WEATHER_CODE_TEXT: WeatherCodeMapping = {
  0: "Clear sky",
  1: "Mainly clear",
  2: "Partly cloudy",
  3: "Overcast",
  45: "Fog",
  48: "Depositing rime fog",
  51: "Light drizzle",
  53: "Moderate drizzle",
  55: "Dense drizzle",
  56: "Light freezing drizzle",
  57: "Dense freezing drizzle",
  61: "Slight rain",
  63: "Moderate rain",
  65: "Heavy rain",
  66: "Light freezing rain",
  67: "Heavy freezing rain",
  71: "Slight snow",
  73: "Moderate snow",
  75: "Heavy snow",
  77: "Snow grains",
  80: "Slight rain showers",
  81: "Moderate rain showers",
  82: "Violent rain showers",
  85: "Slight snow showers",
  86: "Heavy snow showers",
  95: "Thunderstorm",
  96: "Thunderstorm with slight hail",
  99: "Thunderstorm with heavy hail",
};

export function weatherCodeToText(code: WeatherCode): string {
  return WEATHER_CODE_TEXT[code] || "Unknown";
}

// This function will be updated to return icon names instead of emojis
export function getWeatherIconName(code: WeatherCode, isDay: number = 1): string {
  // Clear sky
  if ([0, 1].includes(code)) return isDay ? "sun" : "moon";
  // Cloudy
  if ([2, 3].includes(code)) return "cloud";
  // Rain
  if ([51, 53, 55, 61, 63, 65, 80, 81, 82].includes(code)) return "cloud-rain";
  // Snow
  if ([71, 73, 75, 77, 85, 86].includes(code)) return "snowflake";
  // Fog
  if ([45, 48].includes(code)) return "cloud-fog";
  // Thunderstorm
  if ([95, 96, 99].includes(code)) return "zap";
  // Default
  return "cloud";
}

// Utility functions
export function pressureToPreferred(unit: string, pressureMsl: number | null): PressureData | null {
  if (pressureMsl == null) return null;
  return {
    hpa: pressureMsl,
    inhg: +(pressureMsl * 0.0295299830714).toFixed(2),
  };
}

export function aqiCategory(usAqi: number | null): AQICategory {
  if (usAqi == null) return { label: "N/A", color: "gray" };
  if (usAqi <= 50) return { label: "Good", color: "green" };
  if (usAqi <= 100) return { label: "Moderate", color: "yellow" };
  if (usAqi <= 150) return { label: "Unhealthy (SG)", color: "orange" };
  if (usAqi <= 200) return { label: "Unhealthy", color: "red" };
  if (usAqi <= 300) return { label: "Very Unhealthy", color: "purple" };
  return { label: "Hazardous", color: "maroon" };
}

// Local storage utilities
export function persistLocal<T>(key: LocalStorageKey, value: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.warn(`Failed to persist to localStorage:`, error);
  }
}

export function readLocal<T>(key: LocalStorageKey, fallback: T): T {
  try {
    const value = localStorage.getItem(key);
    return value ? JSON.parse(value) : fallback;
  } catch (error) {
    console.warn(`Failed to read from localStorage:`, error);
    return fallback;
  }
}
