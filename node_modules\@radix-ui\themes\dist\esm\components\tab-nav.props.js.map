{"version": 3, "sources": ["../../../src/components/tab-nav.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst tabNavLinkPropDefs = {\n  ...asChildPropDef,\n  active: { type: 'boolean', default: false },\n} satisfies {\n  active: PropDef<boolean>;\n};\n\nexport { baseTabListPropDefs as tabNavRootPropDefs } from './_internal/base-tab-list.props.js';\nexport { tabNavLinkPropDefs };\n"], "mappings": "AAAA,OAAS,kBAAAA,MAAsB,4BAI/B,MAAMC,EAAqB,CACzB,GAAGD,EACH,OAAQ,CAAE,KAAM,UAAW,QAAS,EAAM,CAC5C,EAIA,OAAgC,uBAAvBE,MAAiD", "names": ["asChildPropDef", "tabNavLinkPropDefs", "baseTabListPropDefs"]}