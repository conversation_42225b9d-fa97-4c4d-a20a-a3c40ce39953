declare const avatarPropDefs: {
    fallback: {
        type: "ReactNode";
        required: true;
    };
    radius: {
        type: "enum";
        values: readonly ["none", "small", "medium", "large", "full"];
        default: undefined;
    };
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: "gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky";
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4", "5", "6", "7", "8", "9"];
        default: "3";
        responsive: true;
    };
    variant: {
        type: "enum";
        className: string;
        values: readonly ["solid", "soft"];
        default: "soft";
    };
    asChild: {
        type: "boolean";
    };
};
export { avatarPropDefs };
//# sourceMappingURL=avatar.props.d.ts.map