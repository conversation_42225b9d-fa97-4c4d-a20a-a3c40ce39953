'use client';

import React from 'react';
import { IconButton, Tooltip } from '@radix-ui/themes';
import { Heart } from 'lucide-react';
import type { FavoriteButtonProps } from '@/types';

export function FavoriteButton({
  location,
  favorites,
  onToggleFavorite,
  className = '',
}: FavoriteButtonProps): React.ReactElement {
  const isFavorite = location && favorites.some(
    (fav) => 
      fav.latitude === location.latitude && 
      fav.longitude === location.longitude && 
      fav.name === location.name
  );

  const tooltipContent = isFavorite ? "Remove from favorites" : "Add to favorites";

  return (
    <Tooltip content={tooltipContent}>
      <IconButton
        onClick={onToggleFavorite}
        variant={isFavorite ? "solid" : "soft"}
        color={isFavorite ? "red" : undefined}
        disabled={!location}
        className={className}
        style={{ minHeight: '44px', minWidth: '44px' }}
      >
        <Heart size={16} fill={isFavorite ? "currentColor" : "none"} />
      </IconButton>
    </Tooltip>
  );
}
