import * as React from 'react';
import { Dialog as DialogPrimitive } from 'radix-ui';
import { Heading } from './heading.js';
import { Text } from './text.js';
import type { DialogContentOwnProps } from './dialog.props.js';
import type { ComponentPropsWithout, RemovedProps, ComponentPropsAs } from '../helpers/component-props.js';
interface DialogRootProps extends ComponentPropsWithout<typeof DialogPrimitive.Root, 'modal'> {
}
declare const DialogRoot: React.FC<DialogRootProps>;
interface DialogTriggerProps extends ComponentPropsWithout<typeof DialogPrimitive.Trigger, RemovedProps> {
}
declare const DialogTrigger: React.ForwardRefExoticComponent<DialogTriggerProps & React.RefAttributes<HTMLButtonElement>>;
interface DialogContentProps extends ComponentPropsWithout<typeof DialogPrimitive.Content, RemovedProps>, DialogContentOwnProps {
    container?: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>['container'];
}
declare const DialogContent: React.ForwardRefExoticComponent<DialogContentProps & React.RefAttributes<HTMLDivElement>>;
type DialogTitleProps = ComponentPropsWithout<typeof Heading, 'asChild'>;
declare const DialogTitle: React.ForwardRefExoticComponent<DialogTitleProps & React.RefAttributes<HTMLHeadingElement>>;
type DialogDescriptionProps = ComponentPropsAs<typeof Text, 'p'>;
declare const DialogDescription: React.ForwardRefExoticComponent<DialogDescriptionProps & React.RefAttributes<HTMLParagraphElement>>;
interface DialogCloseProps extends ComponentPropsWithout<typeof DialogPrimitive.Close, RemovedProps> {
}
declare const DialogClose: React.ForwardRefExoticComponent<DialogCloseProps & React.RefAttributes<HTMLButtonElement>>;
export { DialogRoot as Root, DialogTrigger as Trigger, DialogContent as Content, DialogTitle as Title, DialogDescription as Description, DialogClose as Close, };
export type { DialogRootProps as RootProps, DialogTriggerProps as TriggerProps, DialogContentProps as ContentProps, DialogTitleProps as TitleProps, DialogDescriptionProps as DescriptionProps, DialogCloseProps as CloseProps, };
//# sourceMappingURL=dialog.d.ts.map