@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  /* Mobile-first optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

/* Ensure touch targets are at least 44px for accessibility */
button,
[role="button"],
input[type="button"],
input[type="submit"],
input[type="reset"] {
  min-height: 44px;
  min-width: 44px;
}

/* Improve touch scrolling on mobile */
* {
  -webkit-overflow-scrolling: touch;
}

/* Prevent zoom on input focus on iOS */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px;
  }
}

/* Responsive container adjustments */
@media screen and (max-width: 640px) {
  .rt-Container {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
}

/* Improve card spacing on mobile */
@media screen and (max-width: 768px) {
  .rt-Card {
    margin-bottom: 8px;
  }

  /* Ensure proper spacing for grid items */
  .rt-Grid {
    gap: 8px !important;
  }
}

/* Optimize text sizes for mobile readability */
@media screen and (max-width: 640px) {
  .rt-Heading {
    line-height: 1.2;
  }

  .rt-Text {
    line-height: 1.4;
  }
}
