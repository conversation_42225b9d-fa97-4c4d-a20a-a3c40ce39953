import React from 'react';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface IconProps extends ComponentPropsWithout<'svg', RemovedProps | 'children'> {
}
declare const ThickDividerHorizontalIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;
declare const ThickCheckIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;
declare const ChevronDownIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;
declare const ThickChevronRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;
export { ChevronDownIcon, ThickCheckIcon, ThickChevronRightIcon, ThickDividerHorizontalIcon };
export type { IconProps };
//# sourceMappingURL=icons.d.ts.map