import { ReactNode } from 'react';
import { 
  Location, 
  WeatherData, 
  AQIData, 
  Units, 
  WeatherCurrent, 
  WeatherDaily, 
  WeatherHourly 
} from './weather';

// Base component props
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

// Search Bar Component Props
export interface SearchBarProps extends BaseComponentProps {
  query: string;
  onQueryChange: (query: string) => void;
  results: Location[];
  onLocationSelect: (location: Location) => void;
  onClearResults: () => void;
  onUseGPS: () => void;
  loading?: boolean;
}

// Favorites List Component Props
export interface FavoritesListProps extends BaseComponentProps {
  favorites: Location[];
  onLocationSelect: (location: Location) => void;
}

// Units Selector Component Props
export interface UnitsSelectorProps extends BaseComponentProps {
  units: Units;
  onUnitsChange: (units: Units) => void;
}

// Current Weather Component Props
export interface CurrentWeatherProps extends BaseComponentProps {
  location: Location;
  current: WeatherCurrent;
  units: Units;
  loading?: boolean;
}

// Weather Forecast Component Props
export interface WeatherForecastProps extends BaseComponentProps {
  daily: WeatherDaily;
  units: Units;
}

// Hourly Forecast Component Props
export interface HourlyForecastProps extends BaseComponentProps {
  hourly: WeatherHourly;
  units: Units;
  hoursToShow?: number;
}

// Air Quality Component Props
export interface AirQualityProps extends BaseComponentProps {
  aqi: AQIData;
}

// Weather Card Component Props
export interface WeatherCardProps extends BaseComponentProps {
  title?: string;
  value: string | number;
  unit?: string;
  subtitle?: string;
  icon?: ReactNode;
  onClick?: () => void;
}

// Favorite Button Props
export interface FavoriteButtonProps extends BaseComponentProps {
  location: Location | null;
  favorites: Location[];
  onToggleFavorite: () => void;
}

// Loading State Props
export interface LoadingStateProps extends BaseComponentProps {
  message?: string;
}

// Error State Props
export interface ErrorStateProps extends BaseComponentProps {
  message: string;
  onRetry?: () => void;
}

// Main Page Props (for testing)
export interface WeatherPageProps {
  initialLocation?: Location;
  initialUnits?: Units;
}
