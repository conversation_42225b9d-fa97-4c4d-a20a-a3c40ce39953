{"version": 3, "sources": ["../../../src/components/tabs.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\n\nconst tabsRootPropDefs = {\n  ...asChildPropDef,\n};\n\nconst tabsContentPropDefs = {\n  ...asChildPropDef,\n};\n\nexport { baseTabListPropDefs as tabsListPropDefs } from './_internal/base-tab-list.props.js';\nexport { tabsRootPropDefs, tabsContentPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,yBAAAE,EAAA,gEAAAC,IAAA,eAAAC,EAAAJ,GAAA,IAAAK,EAA+B,qCAU/BC,EAAwD,8CARxD,MAAMH,EAAmB,CACvB,GAAG,gBACL,EAEMD,EAAsB,CAC1B,GAAG,gBACL", "names": ["tabs_props_exports", "__export", "tabsContentPropDefs", "tabsRootPropDefs", "__toCommonJS", "import_as_child_prop", "import_base_tab_list_props"]}