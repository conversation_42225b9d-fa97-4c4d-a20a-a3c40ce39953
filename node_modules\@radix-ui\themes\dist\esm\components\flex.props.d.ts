import { asChildPropDef } from '../props/as-child.prop.js';
import { gapPropDefs } from '../props/gap.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const flexPropDefs: {
    gap: {
        type: "enum | string";
        className: string;
        customProperties: "--gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    gapX: {
        type: "enum | string";
        className: string;
        customProperties: "--column-gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    gapY: {
        type: "enum | string";
        className: string;
        customProperties: "--row-gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    /**
     * Sets the CSS **display** property.
     * Supports a subset of the corresponding CSS values and responsive objects.
     *
     * @example
     * display="inline-flex"
     * display={{ sm: 'none', lg: 'flex' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/display
     */
    display: {
        type: "enum";
        className: string;
        values: readonly ["none", "inline-flex", "flex"];
        responsive: true;
    };
    /**
     * Sets the CSS **flex-direction** property.
     * Supports the corresponding CSS values and responsive objects.
     *
     * @example
     * direction="column"
     * direction={{ sm: 'column', lg: 'row' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/flex-direction
     */
    direction: {
        type: "enum";
        className: string;
        values: readonly ["row", "column", "row-reverse", "column-reverse"];
        responsive: true;
    };
    /**
     * Sets the CSS **align-items** property.
     * Supports the corresponding CSS values and responsive objects.
     *
     * @example
     * align="center"
     * align={{ sm: 'baseline', lg: 'center' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/align-items
     */
    align: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end", "baseline", "stretch"];
        responsive: true;
    };
    /**
     * Sets the CSS **justify-content** property.
     * Supports a subset of the corresponding CSS values and responsive objects.
     *
     * @example
     * justify="between"
     * justify={{ sm: 'start', lg: 'center' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/justify-content
     */
    justify: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end", "between"];
        parseValue: typeof parseJustifyValue;
        responsive: true;
    };
    /**
     * Sets the CSS **flex-wrap** property.
     * Supports the corresponding CSS values and responsive objects.
     *
     * @example
     * wrap="wrap"
     * wrap={{ sm: 'wrap', lg: 'nowrap' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/flex-wrap
     */
    wrap: {
        type: "enum";
        className: string;
        values: readonly ["nowrap", "wrap", "wrap-reverse"];
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
    /**
     * Controls whether to render **div** or **span**
     *
     * @example
     * as="div"
     * as="span"
     */
    as: {
        type: "enum";
        values: readonly ["div", "span"];
        default: "div";
    };
};
declare function parseJustifyValue(value: string): string;
type FlexOwnProps = GetPropDefTypes<typeof flexPropDefs & typeof gapPropDefs & typeof asChildPropDef>;
export { flexPropDefs };
export type { FlexOwnProps };
//# sourceMappingURL=flex.props.d.ts.map