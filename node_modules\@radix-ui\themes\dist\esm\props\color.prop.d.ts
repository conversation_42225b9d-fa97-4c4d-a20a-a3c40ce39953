declare const accentColors: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
declare const grayColors: readonly ["auto", "gray", "mauve", "slate", "sage", "olive", "sand"];
declare const colorPropDef: {
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: (typeof accentColors)[number] | undefined;
    };
};
declare const accentColorPropDef: {
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: (typeof accentColors)[number];
    };
};
export { accentColorPropDef, colorPropDef, accentColors, grayColors, };
//# sourceMappingURL=color.prop.d.ts.map