{"version": 3, "sources": ["../../../src/components/tab-nav.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { NavigationMenu } from 'radix-ui';\n\nimport { tabNavRootPropDefs } from './tab-nav.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { getSubtree } from '../helpers/get-subtree.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { tabNavLinkPropDefs } from './tab-nav.props.js';\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TabNavRootElement = React.ElementRef<typeof NavigationMenu.Root>;\ntype TabNavRootElementProps = ComponentPropsWithout<'nav', RemovedProps>;\ntype TabNavOwnProps = GetPropDefTypes<typeof tabNavRootPropDefs>;\ninterface TabNavRootProps\n  extends Omit<TabNavRootElementProps, 'defaultValue' | 'dir' | 'color'>,\n    MarginProps,\n    TabNavOwnProps {}\nconst TabNavRoot = React.forwardRef<TabNavRootElement, TabNavRootProps>((props, forwardedRef) => {\n  const { children, className, color, ...rootProps } = extractProps(\n    props,\n    tabNavRootPropDefs,\n    marginPropDefs\n  );\n  return (\n    <NavigationMenu.Root\n      className=\"rt-TabNavRoot\"\n      data-accent-color={color}\n      {...rootProps}\n      asChild={false}\n      ref={forwardedRef}\n    >\n      <NavigationMenu.List\n        className={classNames('rt-reset', 'rt-BaseTabList', 'rt-TabNavList', className)}\n      >\n        {children}\n      </NavigationMenu.List>\n    </NavigationMenu.Root>\n  );\n});\nTabNavRoot.displayName = 'TabNav.Root';\n\ntype TabNavLinkElement = React.ElementRef<typeof NavigationMenu.Link>;\ntype TabNavLinkOwnProps = GetPropDefTypes<typeof tabNavLinkPropDefs>;\ninterface TabNavLinkProps\n  extends ComponentPropsWithout<typeof NavigationMenu.Link, RemovedProps | 'onSelect'>,\n    TabNavLinkOwnProps {}\nconst TabNavLink = React.forwardRef<TabNavLinkElement, TabNavLinkProps>((props, forwardedRef) => {\n  const { asChild, children, className, ...linkProps } = props;\n\n  return (\n    <NavigationMenu.Item className=\"rt-TabNavItem\">\n      <NavigationMenu.Link\n        {...linkProps}\n        ref={forwardedRef}\n        className={classNames('rt-reset', 'rt-BaseTabListTrigger', 'rt-TabNavLink', className)}\n        onSelect={undefined}\n        asChild={asChild}\n      >\n        {getSubtree({ asChild, children }, (children) => (\n          <>\n            <span className=\"rt-BaseTabListTriggerInner rt-TabNavLinkInner\">{children}</span>\n            <span className=\"rt-BaseTabListTriggerInnerHidden rt-TabNavLinkInnerHidden\">\n              {children}\n            </span>\n          </>\n        ))}\n      </NavigationMenu.Link>\n    </NavigationMenu.Item>\n  );\n});\nTabNavLink.displayName = 'TabNav.Link';\n\nexport { TabNavRoot as Root, TabNavLink as Link };\nexport type { TabNavRootProps as RootProps, TabNavLinkProps as LinkProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,kBAAAC,MAAsB,WAE/B,OAAS,sBAAAC,MAA0B,qBACnC,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,cAAAC,MAAkB,4BAC3B,OAAS,kBAAAC,MAAsB,2BAc/B,MAAMC,EAAaP,EAAM,WAA+C,CAACQ,EAAOC,IAAiB,CAC/F,KAAM,CAAE,SAAAC,EAAU,UAAAC,EAAW,MAAAC,EAAO,GAAGC,CAAU,EAAIT,EACnDI,EACAL,EACAG,CACF,EACA,OACEN,EAAA,cAACE,EAAe,KAAf,CACC,UAAU,gBACV,oBAAmBU,EAClB,GAAGC,EACJ,QAAS,GACT,IAAKJ,GAELT,EAAA,cAACE,EAAe,KAAf,CACC,UAAWD,EAAW,WAAY,iBAAkB,gBAAiBU,CAAS,GAE7ED,CACH,CACF,CAEJ,CAAC,EACDH,EAAW,YAAc,cAOzB,MAAMO,EAAad,EAAM,WAA+C,CAACQ,EAAOC,IAAiB,CAC/F,KAAM,CAAE,QAAAM,EAAS,SAAAL,EAAU,UAAAC,EAAW,GAAGK,CAAU,EAAIR,EAEvD,OACER,EAAA,cAACE,EAAe,KAAf,CAAoB,UAAU,iBAC7BF,EAAA,cAACE,EAAe,KAAf,CACE,GAAGc,EACJ,IAAKP,EACL,UAAWR,EAAW,WAAY,wBAAyB,gBAAiBU,CAAS,EACrF,SAAU,OACV,QAASI,GAERV,EAAW,CAAE,QAAAU,EAAS,SAAAL,CAAS,EAAIA,GAClCV,EAAA,cAAAA,EAAA,cACEA,EAAA,cAAC,QAAK,UAAU,iDAAiDU,CAAS,EAC1EV,EAAA,cAAC,QAAK,UAAU,6DACbU,CACH,CACF,CACD,CACH,CACF,CAEJ,CAAC,EACDI,EAAW,YAAc", "names": ["React", "classNames", "NavigationMenu", "tabNavRootPropDefs", "extractProps", "getSubtree", "marginPropDefs", "TabNavRoot", "props", "forwardedRef", "children", "className", "color", "rootProps", "TabNavLink", "<PERSON><PERSON><PERSON><PERSON>", "linkProps"]}