import { asChildPropDef } from '../props/as-child.prop.js';
import { widthPropDefs } from '../props/width.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const dialogContentPropDefs: {
    height: {
        type: "string";
        className: string;
        customProperties: "--height"[];
        responsive: true;
    };
    minHeight: {
        type: "string";
        className: string;
        customProperties: "--min-height"[];
        responsive: true;
    };
    maxHeight: {
        type: "string";
        className: string;
        customProperties: "--max-height"[];
        responsive: true;
    };
    align: {
        type: "enum";
        className: string;
        values: ("start" | "center")[];
        default: "center";
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4"];
        default: "3";
        responsive: true;
    };
    width: {
        type: "string";
        className: string;
        customProperties: "--width"[];
        responsive: true;
    };
    minWidth: {
        type: "string";
        className: string;
        customProperties: "--min-width"[];
        responsive: true;
    };
    maxWidth: {
        default: string;
        type: "string";
        className: string;
        customProperties: "--max-width"[];
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
};
type DialogContentOwnProps = GetPropDefTypes<typeof dialogContentPropDefs & typeof asChildPropDef & typeof widthPropDefs>;
export { dialogContentPropDefs };
export type { DialogContentOwnProps };
//# sourceMappingURL=dialog.props.d.ts.map