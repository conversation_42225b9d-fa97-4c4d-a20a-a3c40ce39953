'use client';

import { useState, useCallback, useEffect } from 'react';
import { geocodeCity, geocodePostal } from '@/lib/openmeteo';
import type { UseSearchReturn, Location } from '@/types';

export function useSearch(): UseSearchReturn {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Location[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery || searchQuery.length < 2) {
      setResults([]);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Check if the query looks like a postal code (digits only, 3+ characters)
      const isPostalCode = /^\d{3,}$/.test(searchQuery.trim());
      
      const searchResults = isPostalCode 
        ? await geocodePostal(searchQuery.trim())
        : await geocodeCity(searchQuery.trim());
      
      setResults(searchResults);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
  }, []);

  // Debounce search to avoid too many API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, performSearch]);

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    clearResults,
  };
}
