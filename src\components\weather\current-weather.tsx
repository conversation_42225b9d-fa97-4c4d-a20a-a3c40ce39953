'use client';

import React from 'react';
import { Card, Flex, Heading, Text, Grid } from '@radix-ui/themes';
import { LargeWeatherIcon } from '@/lib/weather-icons';
import { CurrentWeatherCard } from '@/components/ui';
import { weatherCodeToText, pressureToPreferred } from '@/lib/openmeteo';
import { Droplets, Wind, Gauge, Thermometer } from 'lucide-react';
import type { CurrentWeatherProps } from '@/types';

export function CurrentWeather({
  location,
  current,
  units,
  loading = false,
  className = '',
}: CurrentWeatherProps): React.ReactElement {
  const pressure = pressureToPreferred("auto", current.pressure_msl);
  const temperatureSymbol = units.temp === "celsius" ? "C" : "F";

  return (
    <Card className={className}>
      <Flex direction="column" gap="3">
        {/* Location Header */}
        <Flex align="center" justify="between" wrap="wrap" gap="2">
          <Heading size="5">{location.name}</Heading>
          <Text color="gray">
            {[location.admin1, location.country].filter(Boolean).join(", ")}
          </Text>
        </Flex>

        {/* Main Weather Display */}
        <Flex align="center" gap="4" wrap="wrap">
          <LargeWeatherIcon 
            code={current.weather_code} 
            isDay={current.is_day}
            className="flex-shrink-0"
          />
          <Flex direction="column" gap="1">
            <Heading size="9">
              {Math.round(current.temperature_2m)}°{temperatureSymbol}
            </Heading>
            <Text color="gray" size="3">
              {weatherCodeToText(current.weather_code)}
            </Text>
          </Flex>
        </Flex>

        {/* Weather Details Grid */}
        <Grid columns={{ initial: "2", sm: "4" }} gap="3">
          <CurrentWeatherCard
            label="Feels like"
            value={Math.round(current.apparent_temperature)}
            unit={`°${temperatureSymbol}`}
            icon={<Thermometer size={20} className="text-orange-500" />}
          />
          
          <CurrentWeatherCard
            label="Humidity"
            value={current.relative_humidity_2m}
            unit="%"
            icon={<Droplets size={20} className="text-blue-500" />}
          />
          
          <CurrentWeatherCard
            label="Wind"
            value={Math.round(current.wind_speed_10m)}
            unit={` ${units.wind}`}
            icon={<Wind size={20} className="text-gray-500" />}
          />
          
          <CurrentWeatherCard
            label="Pressure"
            value={
              units.temp === "celsius" 
                ? `${pressure?.hpa || 0}` 
                : `${pressure?.inhg || 0}`
            }
            unit={units.temp === "celsius" ? " hPa" : " inHg"}
            icon={<Gauge size={20} className="text-purple-500" />}
          />
        </Grid>
      </Flex>
    </Card>
  );
}
