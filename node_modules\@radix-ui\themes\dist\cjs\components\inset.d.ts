import * as React from 'react';
import { insetPropDefs } from './inset.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type InsetOwnProps = GetPropDefTypes<typeof insetPropDefs>;
interface InsetProps extends ComponentPropsWithout<'div', RemovedProps>, MarginProps, InsetOwnProps {
}
declare const Inset: React.ForwardRefExoticComponent<InsetProps & React.RefAttributes<HTMLDivElement>>;
export { Inset };
export type { InsetProps };
//# sourceMappingURL=inset.d.ts.map