import * as React from 'react';
import { linkPropDefs } from './link.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type LinkOwnProps = GetPropDefTypes<typeof linkPropDefs>;
interface LinkProps extends ComponentPropsWithout<'a', RemovedProps>, MarginProps, LinkOwnProps {
}
declare const Link: React.ForwardRefExoticComponent<LinkProps & React.RefAttributes<HTMLAnchorElement>>;
export { Link };
export type { LinkProps };
//# sourceMappingURL=link.d.ts.map