{"version": 3, "sources": ["../../../src/components/tooltip.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Tooltip as TooltipPrimitive } from 'radix-ui';\n\nimport { Text } from './text.js';\nimport { Theme } from './theme.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { tooltipPropDefs } from './tooltip.props.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TooltipElement = React.ElementRef<typeof TooltipPrimitive.Content>;\ntype TooltipOwnProps = GetPropDefTypes<typeof tooltipPropDefs>;\ninterface TooltipProps\n  extends React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Root>,\n    ComponentPropsWithout<typeof TooltipPrimitive.Content, RemovedProps | 'content'>,\n    TooltipOwnProps {\n  content: React.ReactNode;\n  container?: React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Portal>['container'];\n}\nconst Tooltip = React.forwardRef<TooltipElement, TooltipProps>((props, forwardedRef) => {\n  const {\n    children,\n    className,\n    open,\n    defaultOpen,\n    onOpenChange,\n    delayDuration,\n    disableHoverableContent,\n    content,\n    container,\n    forceMount,\n    ...tooltipContentProps\n  } = extractProps(props, tooltipPropDefs);\n  const rootProps = { open, defaultOpen, onOpenChange, delayDuration, disableHoverableContent };\n  return (\n    <TooltipPrimitive.Root {...rootProps}>\n      <TooltipPrimitive.Trigger asChild>{children}</TooltipPrimitive.Trigger>\n      <TooltipPrimitive.Portal container={container} forceMount={forceMount}>\n        <Theme asChild>\n          <TooltipPrimitive.Content\n            sideOffset={4}\n            collisionPadding={10}\n            {...tooltipContentProps}\n            asChild={false}\n            ref={forwardedRef}\n            className={classNames('rt-TooltipContent', className)}\n          >\n            <Text as=\"p\" className=\"rt-TooltipText\" size=\"1\">\n              {content}\n            </Text>\n            <TooltipPrimitive.Arrow className=\"rt-TooltipArrow\" />\n          </TooltipPrimitive.Content>\n        </Theme>\n      </TooltipPrimitive.Portal>\n    </TooltipPrimitive.Root>\n  );\n});\nTooltip.displayName = 'Tooltip';\n\nexport { Tooltip };\nexport type { TooltipProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,WAAWC,MAAwB,WAE5C,OAAS,QAAAC,MAAY,YACrB,OAAS,SAAAC,MAAa,aACtB,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,mBAAAC,MAAuB,qBAchC,MAAMC,EAAUP,EAAM,WAAyC,CAACQ,EAAOC,IAAiB,CACtF,KAAM,CACJ,SAAAC,EACA,UAAAC,EACA,KAAAC,EACA,YAAAC,EACA,aAAAC,EACA,cAAAC,EACA,wBAAAC,EACA,QAAAC,EACA,UAAAC,EACA,WAAAC,EACA,GAAGC,CACL,EAAIf,EAAaG,EAAOF,CAAe,EACjCe,EAAY,CAAE,KAAAT,EAAM,YAAAC,EAAa,aAAAC,EAAc,cAAAC,EAAe,wBAAAC,CAAwB,EAC5F,OACEhB,EAAA,cAACE,EAAiB,KAAjB,CAAuB,GAAGmB,GACzBrB,EAAA,cAACE,EAAiB,QAAjB,CAAyB,QAAO,IAAEQ,CAAS,EAC5CV,EAAA,cAACE,EAAiB,OAAjB,CAAwB,UAAWgB,EAAW,WAAYC,GACzDnB,EAAA,cAACI,EAAA,CAAM,QAAO,IACZJ,EAAA,cAACE,EAAiB,QAAjB,CACC,WAAY,EACZ,iBAAkB,GACjB,GAAGkB,EACJ,QAAS,GACT,IAAKX,EACL,UAAWR,EAAW,oBAAqBU,CAAS,GAEpDX,EAAA,cAACG,EAAA,CAAK,GAAG,IAAI,UAAU,iBAAiB,KAAK,KAC1Cc,CACH,EACAjB,EAAA,cAACE,EAAiB,MAAjB,CAAuB,UAAU,kBAAkB,CACtD,CACF,CACF,CACF,CAEJ,CAAC,EACDK,EAAQ,YAAc", "names": ["React", "classNames", "TooltipPrimitive", "Text", "Theme", "extractProps", "tooltipPropDefs", "<PERSON><PERSON><PERSON>", "props", "forwardedRef", "children", "className", "open", "defaultOpen", "onOpenChange", "delayDuration", "disableHover<PERSON><PERSON><PERSON>nt", "content", "container", "forceMount", "tooltipContentProps", "rootProps"]}