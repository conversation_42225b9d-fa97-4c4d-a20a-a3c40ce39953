{"version": 3, "sources": ["../../../src/components/tab-nav.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst tabNavLinkPropDefs = {\n  ...asChildPropDef,\n  active: { type: 'boolean', default: false },\n} satisfies {\n  active: PropDef<boolean>;\n};\n\nexport { baseTabListPropDefs as tabNavRootPropDefs } from './_internal/base-tab-list.props.js';\nexport { tabNavLinkPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,wBAAAE,EAAA,8DAAAC,EAAAH,GAAA,IAAAI,EAA+B,qCAW/BC,EAA0D,8CAP1D,MAAMH,EAAqB,CACzB,GAAG,iBACH,OAAQ,CAAE,KAAM,UAAW,QAAS,EAAM,CAC5C", "names": ["tab_nav_props_exports", "__export", "tabNavLinkPropDefs", "__toCommonJS", "import_as_child_prop", "import_base_tab_list_props"]}