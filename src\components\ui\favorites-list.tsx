'use client';

import React from 'react';
import { Box, Card, Flex, Text } from '@radix-ui/themes';
import { MapPin } from 'lucide-react';
import type { FavoritesListProps } from '@/types';

export function FavoritesList({
  favorites,
  onLocationSelect,
  className = '',
}: FavoritesListProps): React.ReactElement | null {
  if (favorites.length === 0) {
    return null;
  }

  return (
    <Box mt="3" className={className}>
      <Text weight="medium" mb="2">
        Favorites
      </Text>
      <Flex mt="2" gap="2" wrap="wrap">
        {favorites.map((favorite, index) => (
          <Card 
            key={`${favorite.name}-${favorite.latitude}-${favorite.longitude}-${index}`}
            className="cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={() => onLocationSelect(favorite)}
          >
            <Flex align="center" gap="2" p="2" style={{ minWidth: '120px' }}>
              <MapPin size={14} className="text-gray-500 flex-shrink-0" />
              <Flex direction="column" gap="1" style={{ minWidth: 0 }}>
                <Text size="2" weight="medium" truncate>
                  {favorite.name}
                </Text>
                {favorite.country && (
                  <Text color="gray" size="1" truncate>
                    {favorite.country}
                  </Text>
                )}
              </Flex>
            </Flex>
          </Card>
        ))}
      </Flex>
    </Box>
  );
}
