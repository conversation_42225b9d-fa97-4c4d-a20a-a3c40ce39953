import * as React from 'react';
import { HoverCard as HoverCardPrimitive } from 'radix-ui';
import type { HoverCardContentOwnProps } from './hover-card.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface HoverCardRootProps extends React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Root> {
}
declare const HoverCardRoot: React.FC<HoverCardRootProps>;
interface HoverCardTriggerProps extends ComponentPropsWithout<typeof HoverCardPrimitive.Trigger, RemovedProps> {
}
declare const HoverCardTrigger: React.ForwardRefExoticComponent<HoverCardTriggerProps & React.RefAttributes<HTMLAnchorElement>>;
interface HoverCardContentProps extends ComponentPropsWithout<typeof HoverCardPrimitive.Content, RemovedProps>, HoverCardContentOwnProps {
    container?: React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Portal>['container'];
}
declare const HoverCardContent: React.ForwardRefExoticComponent<HoverCardContentProps & React.RefAttributes<HTMLDivElement>>;
export { HoverCardRoot as Root, HoverCardTrigger as Trigger, HoverCardContent as Content };
export type { HoverCardRootProps as RootProps, HoverCardTriggerProps as TriggerProps, HoverCardContentProps as ContentProps, };
//# sourceMappingURL=hover-card.d.ts.map