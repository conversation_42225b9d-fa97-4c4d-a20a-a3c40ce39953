// Weather API and data types

export interface Location {
  id?: number;
  name: string;
  latitude: number;
  longitude: number;
  admin1?: string;
  country?: string;
  country_code?: string;
  timezone?: string;
}

export interface WeatherCurrent {
  temperature_2m: number;
  apparent_temperature: number;
  weather_code: number;
  relative_humidity_2m: number;
  wind_speed_10m: number;
  pressure_msl: number;
  is_day: number;
  time: string;
}

export interface WeatherDaily {
  time: string[];
  temperature_2m_max: number[];
  temperature_2m_min: number[];
  sunrise: string[];
  sunset: string[];
  weather_code: number[];
}

export interface WeatherHourly {
  time: string[];
  temperature_2m: number[];
  weather_code: number[];
}

export interface WeatherData {
  current: WeatherCurrent;
  daily: WeatherDaily;
  hourly: WeatherHourly;
  timezone: string;
  timezone_abbreviation: string;
  elevation: number;
}

export interface AQIHourly {
  time: string[];
  pm10: number[];
  pm2_5: number[];
  us_aqi: number[];
}

export interface AQIData {
  hourly: AQIHourly;
  timezone: string;
  timezone_abbreviation: string;
}

export interface AQICategory {
  label: string;
  color: string;
}

export interface PressureData {
  hpa: number;
  inhg: number;
}

export type TemperatureUnit = 'celsius' | 'fahrenheit';
export type WindUnit = 'kmh' | 'mph' | 'ms' | 'kn';

export interface Units {
  temp: TemperatureUnit;
  wind: WindUnit;
}

export interface WeatherFetchOptions {
  latitude: number;
  longitude: number;
  tempUnit?: TemperatureUnit;
  windUnit?: WindUnit;
  hours?: number;
}

export interface GeocodingOptions {
  count?: number;
}

export interface PostalGeocodingOptions extends GeocodingOptions {
  country?: string;
}

// Weather code mappings
export type WeatherCode = 
  | 0 | 1 | 2 | 3 
  | 45 | 48 
  | 51 | 53 | 55 | 56 | 57 
  | 61 | 63 | 65 | 66 | 67 
  | 71 | 73 | 75 | 77 
  | 80 | 81 | 82 | 85 | 86 
  | 95 | 96 | 99;

export interface WeatherCodeMapping {
  [key: number]: string;
}
