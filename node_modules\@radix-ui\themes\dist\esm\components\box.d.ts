import * as React from 'react';
import type { LayoutProps } from '../props/layout.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { BoxOwnProps } from './box.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface CommonBoxProps extends MarginProps, LayoutProps, BoxOwnProps {
}
type BoxDivProps = {
    as?: 'div';
} & ComponentPropsWithout<'div', RemovedProps>;
type BoxSpanProps = {
    as: 'span';
} & ComponentPropsWithout<'span', RemovedProps>;
type BoxProps = CommonBoxProps & (BoxSpanProps | BoxDivProps);
declare const Box: React.ForwardRefExoticComponent<BoxProps & React.RefAttributes<HTMLDivElement>>;
export { Box };
export type { BoxProps };
//# sourceMappingURL=box.d.ts.map