import * as React from 'react';
import { Select as SelectPrimitive } from 'radix-ui';
import { selectRootPropDefs, selectTriggerPropDefs, selectContentPropDefs } from './select.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
type SelectRootOwnProps = GetPropDefTypes<typeof selectRootPropDefs>;
type SelectContextValue = SelectRootOwnProps;
interface SelectRootProps extends SelectPrimitive.SelectProps, SelectContextValue {
}
declare const SelectRoot: React.FC<SelectRootProps>;
type SelectTriggerOwnProps = GetPropDefTypes<typeof selectTriggerPropDefs>;
interface SelectTriggerProps extends ComponentPropsWithout<typeof SelectPrimitive.Trigger, RemovedProps>, MarginProps, SelectTriggerOwnProps {
}
declare const SelectTrigger: React.ForwardRefExoticComponent<SelectTriggerProps & React.RefAttributes<HTMLButtonElement>>;
type SelectContentOwnProps = GetPropDefTypes<typeof selectContentPropDefs>;
interface SelectContentProps extends ComponentPropsWithout<typeof SelectPrimitive.Content, RemovedProps>, SelectContentOwnProps {
    container?: React.ComponentPropsWithoutRef<typeof SelectPrimitive.Portal>['container'];
}
declare const SelectContent: React.ForwardRefExoticComponent<SelectContentProps & React.RefAttributes<HTMLDivElement>>;
interface SelectItemProps extends ComponentPropsWithout<typeof SelectPrimitive.Item, RemovedProps> {
}
declare const SelectItem: React.ForwardRefExoticComponent<SelectItemProps & React.RefAttributes<HTMLDivElement>>;
interface SelectGroupProps extends ComponentPropsWithout<typeof SelectPrimitive.Group, RemovedProps> {
}
declare const SelectGroup: React.ForwardRefExoticComponent<SelectGroupProps & React.RefAttributes<HTMLDivElement>>;
interface SelectLabelProps extends ComponentPropsWithout<typeof SelectPrimitive.Label, RemovedProps> {
}
declare const SelectLabel: React.ForwardRefExoticComponent<SelectLabelProps & React.RefAttributes<HTMLDivElement>>;
interface SelectSeparatorProps extends ComponentPropsWithout<typeof SelectPrimitive.Separator, RemovedProps> {
}
declare const SelectSeparator: React.ForwardRefExoticComponent<SelectSeparatorProps & React.RefAttributes<HTMLDivElement>>;
export { SelectRoot as Root, SelectTrigger as Trigger, SelectContent as Content, SelectItem as Item, SelectGroup as Group, SelectLabel as Label, SelectSeparator as Separator, };
export type { SelectRootProps as RootProps, SelectTriggerProps as TriggerProps, SelectContentProps as ContentProps, SelectItemProps as ItemProps, SelectGroupProps as GroupProps, SelectLabelProps as LabelProps, SelectSeparatorProps as SeparatorProps, };
//# sourceMappingURL=select.d.ts.map