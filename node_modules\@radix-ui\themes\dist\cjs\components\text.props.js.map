{"version": 3, "sources": ["../../../src/components/text.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\nimport { colorPropDef } from '../props/color.prop.js';\nimport { highContrastPropDef } from '../props/high-contrast.prop.js';\nimport { leadingTrimPropDef } from '../props/leading-trim.prop.js';\nimport { textAlignPropDef } from '../props/text-align.prop.js';\nimport { textWrapPropDef } from '../props/text-wrap.prop.js';\nimport { truncatePropDef } from '../props/truncate.prop.js';\nimport { weightPropDef } from '../props/weight.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst as = ['span', 'div', 'label', 'p'] as const;\nconst sizes = ['1', '2', '3', '4', '5', '6', '7', '8', '9'] as const;\n\nconst textPropDefs = {\n  as: { type: 'enum', values: as, default: 'span' },\n  ...asChildPropDef,\n  size: {\n    type: 'enum',\n    className: 'rt-r-size',\n    values: sizes,\n    responsive: true,\n  },\n  ...weightPropDef,\n  ...textAlignPropDef,\n  ...leadingTrimPropDef,\n  ...truncatePropDef,\n  ...textWrapPropDef,\n  ...colorPropDef,\n  ...highContrastPropDef,\n} satisfies {\n  as: PropDef<(typeof as)[number]>;\n  size: PropDef<(typeof sizes)[number]>;\n};\n\nexport { textPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,kBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA+B,qCAC/BC,EAA6B,kCAC7BC,EAAoC,0CACpCC,EAAmC,yCACnCC,EAAiC,uCACjCC,EAAgC,sCAChCC,EAAgC,qCAChCC,EAA8B,mCAI9B,MAAMC,EAAK,CAAC,OAAQ,MAAO,QAAS,GAAG,EACjCC,EAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAEpDX,EAAe,CACnB,GAAI,CAAE,KAAM,OAAQ,OAAQU,EAAI,QAAS,MAAO,EAChD,GAAG,iBACH,KAAM,CACJ,KAAM,OACN,UAAW,YACX,OAAQC,EACR,WAAY,EACd,EACA,GAAG,gBACH,GAAG,mBACH,GAAG,qBACH,GAAG,kBACH,GAAG,kBACH,GAAG,eACH,GAAG,qBACL", "names": ["text_props_exports", "__export", "textPropDefs", "__toCommonJS", "import_as_child_prop", "import_color_prop", "import_high_contrast_prop", "import_leading_trim_prop", "import_text_align_prop", "import_text_wrap_prop", "import_truncate_prop", "import_weight_prop", "as", "sizes"]}