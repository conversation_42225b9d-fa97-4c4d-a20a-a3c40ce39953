{"version": 3, "sources": ["../../../src/components/spinner.props.tsx"], "sourcesContent": ["import type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\n\nconst spinnerPropDefs = {\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n  loading: { type: 'boolean', default: true },\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  loading: PropDef<boolean>;\n};\n\nexport { spinnerPropDefs };\n"], "mappings": "AAEA,MAAMA,EAAQ,CAAC,IAAK,IAAK,GAAG,EAEtBC,EAAkB,CACtB,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQD,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,QAAS,CAAE,KAAM,UAAW,QAAS,EAAK,CAC5C", "names": ["sizes", "spinnerPropDefs"]}