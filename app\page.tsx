'use client';

import React, { useState } from 'react';
import { Box, Container, Heading, Text, Kbd, Separator } from '@radix-ui/themes';

// Import our new modular components
import {
  SearchBar,
  FavoritesList,
  UnitsSelector,
  FavoriteButton,
  LoadingState,
  ErrorState,
} from '@/components/ui';
import {
  CurrentWeather,
  WeatherForecast,
  HourlyForecast,
  AirQuality,
} from '@/components/weather';

// Import custom hooks
import {
  useLocalStorage,
  useGeolocation,
  useFavorites,
  useWeatherData,
  useSearch,
  useUnits,
} from '@/hooks';

// Import types
import type { Location } from '@/types';

export default function Home(): React.ReactElement {
  // Local state for selected location
  const { value: selectedLocation, setValue: setSelectedLocation } = useLocalStorage<Location | null>('sel', null);

  // Units management
  const { units, setUnits } = useUnits();

  // Favorites management
  const { favorites, toggleFavorite } = useFavorites();

  // Search functionality
  const { query, setQuery, results, loading: searchLoading, clearResults } = useSearch();

  // Geolocation
  const { getCurrentLocation } = useGeolocation();

  // Weather data fetching
  const { data, aqi, loading: weatherLoading, error } = useWeatherData(selectedLocation, units);

  // Event handlers
  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
    clearResults();
    setQuery(`${location.name}${location.admin1 ? ", " + location.admin1 : ""}${location.country ? ", " + location.country : ""}`);
  };

  const handleUseGPS = async () => {
    try {
      const location = await getCurrentLocation();
      handleLocationSelect(location);
    } catch (error) {
      console.error('Failed to get current location:', error);
    }
  };

  const handleToggleFavorite = () => {
    if (selectedLocation) {
      toggleFavorite(selectedLocation);
    }
  };

  return (
    <Container size="3">
      {/* Header */}
      <Box py="4">
        <Heading size="6">Weather</Heading>
        <Text color="gray">Powered by Open-Meteo</Text>
      </Box>

      {/* Search Bar with GPS and Favorite Button */}
      <Box mb="3">
        <SearchBar
          query={query}
          onQueryChange={setQuery}
          results={results}
          onLocationSelect={handleLocationSelect}
          onClearResults={clearResults}
          onUseGPS={handleUseGPS}
          loading={searchLoading}
        />
        {selectedLocation && (
          <Box mt="2">
            <FavoriteButton
              location={selectedLocation}
              favorites={favorites}
              onToggleFavorite={handleToggleFavorite}
            />
          </Box>
        )}
      </Box>

      {/* Favorites List */}
      <FavoritesList
        favorites={favorites}
        onLocationSelect={handleLocationSelect}
      />

      {/* Units Selector */}
      <UnitsSelector
        units={units}
        onUnitsChange={setUnits}
      />

      {/* Weather Display */}
      <Box mt="4">
        {!selectedLocation && (
          <Text color="gray">
            Search for a location to see weather. Tip: use your GPS with the target icon.
          </Text>
        )}

        {weatherLoading && (
          <LoadingState message="Loading weather data..." />
        )}

        {error && (
          <ErrorState
            message={error}
            onRetry={() => window.location.reload()}
          />
        )}

        {selectedLocation && data?.current && (
          <Box>
            {/* Current Weather */}
            <CurrentWeather
              location={selectedLocation}
              current={data.current}
              units={units}
              loading={weatherLoading}
            />

            {/* 7-Day Forecast */}
            {data.daily && (
              <Box mt="4">
                <WeatherForecast
                  daily={data.daily}
                  units={units}
                />
              </Box>
            )}

            {/* Hourly Forecast */}
            {data.hourly && (
              <Box mt="4">
                <HourlyForecast
                  hourly={data.hourly}
                  units={units}
                  hoursToShow={12}
                />
              </Box>
            )}

            {/* Air Quality */}
            {aqi && (
              <Box mt="4">
                <AirQuality aqi={aqi} />
              </Box>
            )}
          </Box>
        )}
      </Box>

      {/* Footer */}
      <Box my="5">
        <Separator size="4" />
        <Text size="1" color="gray">
          Shortcuts: <Kbd>Ctrl</Kbd> + <Kbd>K</Kbd> to focus search
        </Text>
      </Box>
    </Container>
  );
}
