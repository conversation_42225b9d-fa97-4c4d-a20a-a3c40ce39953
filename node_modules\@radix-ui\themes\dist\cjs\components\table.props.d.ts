declare const tableRootPropDefs: {
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3"];
        default: "2";
        responsive: true;
    };
    variant: {
        type: "enum";
        className: string;
        values: readonly ["surface", "ghost"];
        default: "ghost";
    };
    layout: {
        type: "enum";
        className: string;
        values: readonly ["auto", "fixed"];
        responsive: true;
    };
};
declare const tableRowPropDefs: {
    align: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end", "baseline"];
        parseValue: typeof parseAlignValue;
        responsive: true;
    };
};
declare function parseAlignValue(value: string): string | undefined;
declare const tableCellPropDefs: {
    p: {
        type: "enum | string";
        className: string;
        customProperties: "--p"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    px: {
        type: "enum | string";
        className: string;
        customProperties: ("--pl" | "--pr")[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    py: {
        type: "enum | string";
        className: string;
        customProperties: ("--pt" | "--pb")[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    pt: {
        type: "enum | string";
        className: string;
        customProperties: "--pt"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    pr: {
        type: "enum | string";
        className: string;
        customProperties: "--pr"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    pb: {
        type: "enum | string";
        className: string;
        customProperties: "--pb"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    pl: {
        type: "enum | string";
        className: string;
        customProperties: "--pl"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    width: {
        type: "string";
        className: string;
        customProperties: "--width"[];
        responsive: true;
    };
    minWidth: {
        type: "string";
        className: string;
        customProperties: "--min-width"[];
        responsive: true;
    };
    maxWidth: {
        type: "string";
        className: string;
        customProperties: "--max-width"[];
        responsive: true;
    };
    justify: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end"];
        parseValue: typeof parseJustifyValue;
        responsive: true;
    };
};
declare function parseJustifyValue(value: string): string | undefined;
export { tableRootPropDefs, tableRowPropDefs, tableCellPropDefs };
//# sourceMappingURL=table.props.d.ts.map