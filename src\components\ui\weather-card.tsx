'use client';

import React from 'react';
import { Card, Flex, Text, Heading, Inset } from '@radix-ui/themes';
import type { WeatherCardProps } from '@/types';

export function WeatherCard({
  title,
  value,
  unit,
  subtitle,
  icon,
  onClick,
  className = '',
  children,
}: WeatherCardProps): React.ReactElement {
  const cardContent = (
    <Inset>
      <Flex direction="column" gap="1" align="center" justify="center" p="3">
        {icon && (
          <Flex justify="center" mb="1">
            {icon}
          </Flex>
        )}
        {title && (
          <Text size="2" color="gray" align="center">
            {title}
          </Text>
        )}
        <Heading size="6" align="center">
          {value}
          {unit && <Text as="span" size="4" color="gray">{unit}</Text>}
        </Heading>
        {subtitle && (
          <Text size="1" color="gray" align="center">
            {subtitle}
          </Text>
        )}
        {children}
      </Flex>
    </Inset>
  );

  if (onClick) {
    return (
      <Card 
        className={`cursor-pointer hover:bg-gray-50 transition-colors ${className}`}
        onClick={onClick}
      >
        {cardContent}
      </Card>
    );
  }

  return (
    <Card className={className}>
      {cardContent}
    </Card>
  );
}

// Specialized weather card for current conditions
interface CurrentWeatherCardProps {
  label: string;
  value: string | number;
  unit?: string;
  icon?: React.ReactNode;
  className?: string;
}

export function CurrentWeatherCard({
  label,
  value,
  unit,
  icon,
  className = '',
}: CurrentWeatherCardProps): React.ReactElement {
  return (
    <WeatherCard
      title={label}
      value={value}
      unit={unit}
      icon={icon}
      className={className}
    />
  );
}

// Forecast card for daily/hourly forecasts
interface ForecastCardProps {
  time: string;
  temperature: string;
  icon: React.ReactNode;
  subtitle?: string;
  onClick?: () => void;
  className?: string;
}

export function ForecastCard({
  time,
  temperature,
  icon,
  subtitle,
  onClick,
  className = '',
}: ForecastCardProps): React.ReactElement {
  return (
    <WeatherCard
      title={time}
      value={temperature}
      subtitle={subtitle}
      icon={icon}
      onClick={onClick}
      className={className}
    />
  );
}
