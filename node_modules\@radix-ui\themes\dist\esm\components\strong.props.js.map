{"version": 3, "sources": ["../../../src/components/strong.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\nimport { textWrapPropDef } from '../props/text-wrap.prop.js';\nimport { truncatePropDef } from '../props/truncate.prop.js';\n\nconst strongPropDefs = {\n  ...asChildPropDef,\n  ...truncatePropDef,\n  ...textWrapPropDef,\n};\n\nexport { strongPropDefs };\n"], "mappings": "AAAA,OAAS,kBAAAA,MAAsB,4BAC/B,OAAS,mBAAAC,MAAuB,6BAChC,OAAS,mBAAAC,MAAuB,4BAEhC,MAAMC,EAAiB,CACrB,GAAGH,EACH,GAAGE,EACH,GAAGD,CACL", "names": ["asChildPropDef", "textWrapPropDef", "truncatePropDef", "strongPropDefs"]}