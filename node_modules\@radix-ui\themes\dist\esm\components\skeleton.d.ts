import * as React from 'react';
import { skeletonPropDefs } from './skeleton.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
type SkeletonOwnProps = GetPropDefTypes<typeof skeletonPropDefs>;
interface SkeletonProps extends ComponentPropsWithout<'span', RemovedProps>, MarginProps, SkeletonOwnProps {
}
declare const Skeleton: React.ForwardRefExoticComponent<SkeletonProps & React.RefAttributes<HTMLSpanElement>>;
export { Skeleton };
export type { SkeletonProps };
//# sourceMappingURL=skeleton.d.ts.map