'use client';

import React from 'react';
import { Box, Text, Grid } from '@radix-ui/themes';
import { ForecastCard } from '@/components/ui';
import { SmallWeatherIcon } from '@/lib/weather-icons';
import type { WeatherForecastProps } from '@/types';

export function WeatherForecast({
  daily,
  units,
  className = '',
}: WeatherForecastProps): React.ReactElement {
  const temperatureSymbol = units.temp === "celsius" ? "°C" : "°F";

  return (
    <Box className={className}>
      <Text weight="medium" mb="2">
        7-Day Forecast
      </Text>
      <Grid columns={{ initial: "2", sm: "4", md: "7" }} gap="2" mt="2">
        {daily.time.map((time, index) => {
          const date = new Date(time);
          const dayName = date.toLocaleDateString(undefined, { weekday: "short" });
          const maxTemp = Math.round(daily.temperature_2m_max[index]);
          const minTemp = Math.round(daily.temperature_2m_min[index]);
          const weatherCode = daily.weather_code[index];

          return (
            <ForecastCard
              key={time}
              time={dayName}
              temperature={`${maxTemp}° / ${minTemp}°`}
              icon={
                <SmallWeatherIcon 
                  code={weatherCode} 
                  isDay={1} // Use day icon for daily forecast
                />
              }
            />
          );
        })}
      </Grid>
    </Box>
  );
}
