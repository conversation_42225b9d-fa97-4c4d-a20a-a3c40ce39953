{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_6feb203d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_6feb203d-module__8DQF1a__className\",\n  \"variable\": \"geist_6feb203d-module__8DQF1a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_6feb203d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_c7d183a.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_c7d183a-module__ZW1U4G__className\",\n  \"variable\": \"geist_mono_c7d183a-module__ZW1U4G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_c7d183a.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,yJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,yJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,yJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/weather-app/app/layout.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport \"@radix-ui/themes/styles.css\";\nimport { Theme } from \"@radix-ui/themes\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata = {\n  title: \"Weather App\",\n  description: \"Weather powered by Open-Meteo\",\n};\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\">\n      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>\n        <Theme appearance=\"dark\" radius=\"large\" scaling=\"100%\">\n          {children}\n        </Theme>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;;;;;;;AAYO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,6IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBACxE,cAAA,8OAAC,6KAAA,CAAA,QAAK;gBAAC,YAAW;gBAAO,QAAO;gBAAQ,SAAQ;0BAC7C;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/weather-app/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/weather-app/node_modules/%40radix-ui/themes/dist/esm/components/theme.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Theme = registerClientReference(\n    function() { throw new Error(\"Attempted to call Theme() from the server but Theme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/themes/dist/esm/components/theme.js <module evaluation>\",\n    \"Theme\",\n);\nexport const ThemeContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeContext() from the server but ThemeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/themes/dist/esm/components/theme.js <module evaluation>\",\n    \"ThemeContext\",\n);\nexport const useThemeContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeContext() from the server but useThemeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/themes/dist/esm/components/theme.js <module evaluation>\",\n    \"useThemeContext\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,4FACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,4FACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4FACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/weather-app/node_modules/%40radix-ui/themes/dist/esm/components/theme.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Theme = registerClientReference(\n    function() { throw new Error(\"Attempted to call Theme() from the server but Theme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/themes/dist/esm/components/theme.js\",\n    \"Theme\",\n);\nexport const ThemeContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeContext() from the server but ThemeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/themes/dist/esm/components/theme.js\",\n    \"ThemeContext\",\n);\nexport const useThemeContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeContext() from the server but useThemeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/themes/dist/esm/components/theme.js\",\n    \"useThemeContext\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,wEACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,wEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,wEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/weather-app/node_modules/%40radix-ui/themes/src/components/theme.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Direction, Slot, Tooltip as TooltipPrimitive } from 'radix-ui';\n\nimport { getMatchingGrayColor } from '../helpers/get-matching-gray-color.js';\nimport { themePropDefs } from './theme.props.js';\n\nimport type { ThemeOwnProps } from './theme.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\n\nconst noop = () => {};\n\ntype ThemeAppearance = (typeof themePropDefs.appearance.values)[number];\ntype ThemeAccentColor = (typeof themePropDefs.accentColor.values)[number];\ntype ThemeGrayColor = (typeof themePropDefs.grayColor.values)[number];\ntype ThemePanelBackground = (typeof themePropDefs.panelBackground.values)[number];\ntype ThemeRadius = (typeof themePropDefs.radius.values)[number];\ntype ThemeScaling = (typeof themePropDefs.scaling.values)[number];\n\ninterface ThemeChangeHandlers {\n  onAppearanceChange: (appearance: ThemeAppearance) => void;\n  onAccentColorChange: (accentColor: ThemeAccentColor) => void;\n  onGrayColorChange: (grayColor: ThemeGrayColor) => void;\n  onPanelBackgroundChange: (panelBackground: ThemePanelBackground) => void;\n  onRadiusChange: (radius: ThemeRadius) => void;\n  onScalingChange: (scaling: ThemeScaling) => void;\n}\n\ninterface ThemeContextValue extends ThemeChangeHandlers {\n  appearance: ThemeAppearance;\n  accentColor: ThemeAccentColor;\n  grayColor: ThemeGrayColor;\n  resolvedGrayColor: ThemeGrayColor;\n  panelBackground: ThemePanelBackground;\n  radius: ThemeRadius;\n  scaling: ThemeScaling;\n}\nconst ThemeContext = React.createContext<ThemeContextValue | undefined>(undefined);\n\nfunction useThemeContext() {\n  const context = React.useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('`useThemeContext` must be used within a `Theme`');\n  }\n  return context;\n}\n\ninterface ThemeProps extends ThemeImplPublicProps {}\nconst Theme = React.forwardRef<ThemeImplElement, ThemeProps>((props, forwardedRef) => {\n  const context = React.useContext(ThemeContext);\n  const isRoot = context === undefined;\n  if (isRoot) {\n    return (\n      <TooltipPrimitive.Provider delayDuration={200}>\n        <Direction.Provider dir=\"ltr\">\n          <ThemeRoot {...props} ref={forwardedRef} />\n        </Direction.Provider>\n      </TooltipPrimitive.Provider>\n    );\n  }\n  return <ThemeImpl {...props} ref={forwardedRef} />;\n});\nTheme.displayName = 'Theme';\n\nconst ThemeRoot = React.forwardRef<ThemeImplElement, ThemeImplPublicProps>(\n  (props, forwardedRef) => {\n    const {\n      appearance: appearanceProp = themePropDefs.appearance.default,\n      accentColor: accentColorProp = themePropDefs.accentColor.default,\n      grayColor: grayColorProp = themePropDefs.grayColor.default,\n      panelBackground: panelBackgroundProp = themePropDefs.panelBackground.default,\n      radius: radiusProp = themePropDefs.radius.default,\n      scaling: scalingProp = themePropDefs.scaling.default,\n      hasBackground = themePropDefs.hasBackground.default,\n      ...rootProps\n    } = props;\n    const [appearance, setAppearance] = React.useState(appearanceProp);\n    React.useEffect(() => setAppearance(appearanceProp), [appearanceProp]);\n\n    const [accentColor, setAccentColor] = React.useState(accentColorProp);\n    React.useEffect(() => setAccentColor(accentColorProp), [accentColorProp]);\n\n    const [grayColor, setGrayColor] = React.useState(grayColorProp);\n    React.useEffect(() => setGrayColor(grayColorProp), [grayColorProp]);\n\n    const [panelBackground, setPanelBackground] = React.useState(panelBackgroundProp);\n    React.useEffect(() => setPanelBackground(panelBackgroundProp), [panelBackgroundProp]);\n\n    const [radius, setRadius] = React.useState(radiusProp);\n    React.useEffect(() => setRadius(radiusProp), [radiusProp]);\n\n    const [scaling, setScaling] = React.useState(scalingProp);\n    React.useEffect(() => setScaling(scalingProp), [scalingProp]);\n\n    return (\n      <ThemeImpl\n        {...rootProps}\n        ref={forwardedRef}\n        isRoot\n        hasBackground={hasBackground}\n        //\n        appearance={appearance}\n        accentColor={accentColor}\n        grayColor={grayColor}\n        panelBackground={panelBackground}\n        radius={radius}\n        scaling={scaling}\n        //\n        onAppearanceChange={setAppearance}\n        onAccentColorChange={setAccentColor}\n        onGrayColorChange={setGrayColor}\n        onPanelBackgroundChange={setPanelBackground}\n        onRadiusChange={setRadius}\n        onScalingChange={setScaling}\n      />\n    );\n  }\n);\nThemeRoot.displayName = 'ThemeRoot';\n\ntype ThemeImplElement = React.ElementRef<'div'>;\ninterface ThemeImplProps extends ThemeImplPublicProps, ThemeImplPrivateProps {}\ninterface ThemeImplPublicProps\n  extends ComponentPropsWithout<'div', RemovedProps | 'dir'>,\n    ThemeOwnProps {}\ninterface ThemeImplPrivateProps extends Partial<ThemeChangeHandlers> {\n  isRoot?: boolean;\n}\nconst ThemeImpl = React.forwardRef<ThemeImplElement, ThemeImplProps>((props, forwardedRef) => {\n  const context = React.useContext(ThemeContext);\n  const {\n    asChild,\n    isRoot,\n    hasBackground: hasBackgroundProp,\n    //\n    appearance = context?.appearance ?? themePropDefs.appearance.default,\n    accentColor = context?.accentColor ?? themePropDefs.accentColor.default,\n    grayColor = context?.resolvedGrayColor ?? themePropDefs.grayColor.default,\n    panelBackground = context?.panelBackground ?? themePropDefs.panelBackground.default,\n    radius = context?.radius ?? themePropDefs.radius.default,\n    scaling = context?.scaling ?? themePropDefs.scaling.default,\n    //\n    onAppearanceChange = noop,\n    onAccentColorChange = noop,\n    onGrayColorChange = noop,\n    onPanelBackgroundChange = noop,\n    onRadiusChange = noop,\n    onScalingChange = noop,\n    //\n    ...themeProps\n  } = props;\n  const Comp = asChild ? Slot.Root : 'div';\n  const resolvedGrayColor = grayColor === 'auto' ? getMatchingGrayColor(accentColor) : grayColor;\n  const isExplicitAppearance = props.appearance === 'light' || props.appearance === 'dark';\n  const hasBackground =\n    hasBackgroundProp === undefined ? isRoot || isExplicitAppearance : hasBackgroundProp;\n  return (\n    <ThemeContext.Provider\n      value={React.useMemo(\n        () => ({\n          appearance,\n          accentColor,\n          grayColor,\n          resolvedGrayColor,\n          panelBackground,\n          radius,\n          scaling,\n          //\n          onAppearanceChange,\n          onAccentColorChange,\n          onGrayColorChange,\n          onPanelBackgroundChange,\n          onRadiusChange,\n          onScalingChange,\n        }),\n        [\n          appearance,\n          accentColor,\n          grayColor,\n          resolvedGrayColor,\n          panelBackground,\n          radius,\n          scaling,\n          //\n          onAppearanceChange,\n          onAccentColorChange,\n          onGrayColorChange,\n          onPanelBackgroundChange,\n          onRadiusChange,\n          onScalingChange,\n        ]\n      )}\n    >\n      <Comp\n        data-is-root-theme={isRoot ? 'true' : 'false'}\n        data-accent-color={accentColor}\n        data-gray-color={resolvedGrayColor}\n        // for nested `Theme` background\n        data-has-background={hasBackground ? 'true' : 'false'}\n        data-panel-background={panelBackground}\n        data-radius={radius}\n        data-scaling={scaling}\n        ref={forwardedRef}\n        {...themeProps}\n        className={classNames(\n          'radix-themes',\n          {\n            light: appearance === 'light',\n            dark: appearance === 'dark',\n          },\n          themeProps.className\n        )}\n      />\n    </ThemeContext.Provider>\n  );\n});\nThemeImpl.displayName = 'ThemeImpl';\n\nexport { Theme, ThemeContext, useThemeContext };\nexport type { ThemeProps };\n"], "names": ["React", "classNames", "Direction", "Slot", "TooltipPrimitive", "getMatchingGrayColor", "themePropDefs", "noop", "ThemeContext", "useThemeContext", "context", "Theme", "props", "forwardedRef", "ThemeRoot", "ThemeImpl", "appearanceProp", "accentColorProp", "grayColorProp", "panelBackgroundProp", "radiusProp", "scalingProp", "hasBackground", "rootProps", "appearance", "set<PERSON><PERSON><PERSON>ce", "accentColor", "setAccentColor", "grayColor", "setGrayColor", "panelBackground", "setPanelBackground", "radius", "setRadius", "scaling", "setScaling", "<PERSON><PERSON><PERSON><PERSON>", "isRoot", "hasBackgroundProp", "onAppearanceChange", "onAccentColorChange", "onGrayColorChange", "onPanelBackgroundChange", "onRadiusChange", "onScalingChange", "themeProps", "Comp", "resolvedGrayColor", "isExplicitAppearance"], "mappings": "", "debugId": null}}]}