{"version": 3, "sources": ["../../../src/components/tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Tabs as TabsPrimitive } from 'radix-ui';\n\nimport { tabsListPropDefs } from './tabs.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { tabsContentPropDefs, tabsRootPropDefs } from './tabs.props.js';\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TabsRootElement = React.ElementRef<typeof TabsPrimitive.Root>;\ntype TabsRootOwnProps = GetPropDefTypes<typeof tabsRootPropDefs>;\ninterface TabsRootProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>,\n    MarginProps,\n    TabsRootOwnProps {}\nconst TabsRoot = React.forwardRef<TabsRootElement, TabsRootProps>((props, forwardedRef) => {\n  const { className, ...rootProps } = extractProps(props, marginPropDefs);\n  return (\n    <TabsPrimitive.Root\n      {...rootProps}\n      ref={forwardedRef}\n      className={classNames('rt-TabsRoot', className)}\n    />\n  );\n});\nTabsRoot.displayName = 'Tabs.Root';\n\ntype TabsListElement = React.ElementRef<typeof TabsPrimitive.List>;\ntype TabsListOwnProps = GetPropDefTypes<typeof tabsListPropDefs>;\ninterface TabsListProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.List, RemovedProps>,\n    MarginProps,\n    TabsListOwnProps {}\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>((props, forwardedRef) => {\n  const { className, color, ...listProps } = extractProps(props, tabsListPropDefs, marginPropDefs);\n  return (\n    <TabsPrimitive.List\n      data-accent-color={color}\n      {...listProps}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-BaseTabList', 'rt-TabsList', className)}\n    />\n  );\n});\nTabsList.displayName = 'Tabs.List';\n\ntype TabsTriggerElement = React.ElementRef<typeof TabsPrimitive.Trigger>;\ninterface TabsTriggerProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.Trigger, RemovedProps> {}\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props, forwardedRef) => {\n    const { className, children, ...triggerProps } = props;\n    return (\n      <TabsPrimitive.Trigger\n        {...triggerProps}\n        asChild={false}\n        ref={forwardedRef}\n        className={classNames('rt-reset', 'rt-BaseTabListTrigger', 'rt-TabsTrigger', className)}\n      >\n        <span className=\"rt-BaseTabListTriggerInner rt-TabsTriggerInner\">{children}</span>\n        <span className=\"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden\">\n          {children}\n        </span>\n      </TabsPrimitive.Trigger>\n    );\n  }\n);\nTabsTrigger.displayName = 'Tabs.Trigger';\n\ntype TabsContentElement = React.ElementRef<typeof TabsPrimitive.Content>;\ntype TabsContentOwnProps = GetPropDefTypes<typeof tabsContentPropDefs>;\ninterface TabsContentProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.Content, RemovedProps>,\n    MarginProps,\n    TabsContentOwnProps {}\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props, forwardedRef) => {\n    const { className, ...contentProps } = extractProps(props, marginPropDefs);\n    return (\n      <TabsPrimitive.Content\n        {...contentProps}\n        ref={forwardedRef}\n        className={classNames('rt-TabsContent', className)}\n      />\n    );\n  }\n);\nTabsContent.displayName = 'Tabs.Content';\n\nexport { TabsRoot as Root, TabsList as List, TabsTrigger as Trigger, TabsContent as Content };\nexport type {\n  TabsRootProps as RootProps,\n  TabsListProps as ListProps,\n  TabsTriggerProps as TriggerProps,\n  TabsContentProps as ContentProps,\n};\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,EAAA,SAAAC,EAAA,SAAAC,EAAA,YAAAC,IAAA,eAAAC,EAAAN,GAAA,IAAAO,EAAuB,oBACvBC,EAAuB,yBACvBC,EAAsC,oBAEtCC,EAAiC,2BACjCC,EAA6B,uCAC7BC,EAA+B,oCAa/B,MAAMR,EAAWG,EAAM,WAA2C,CAACM,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAC,EAAW,GAAGC,CAAU,KAAI,gBAAaH,EAAO,gBAAc,EACtE,OACEN,EAAA,cAAC,EAAAU,KAAc,KAAd,CACE,GAAGD,EACJ,IAAKF,EACL,aAAW,EAAAI,SAAW,cAAeH,CAAS,EAChD,CAEJ,CAAC,EACDX,EAAS,YAAc,YAQvB,MAAMD,EAAWI,EAAM,WAA2C,CAACM,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAC,EAAW,MAAAI,EAAO,GAAGC,CAAU,KAAI,gBAAaP,EAAO,mBAAkB,gBAAc,EAC/F,OACEN,EAAA,cAAC,EAAAU,KAAc,KAAd,CACC,oBAAmBE,EAClB,GAAGC,EACJ,QAAS,GACT,IAAKN,EACL,aAAW,EAAAI,SAAW,iBAAkB,cAAeH,CAAS,EAClE,CAEJ,CAAC,EACDZ,EAAS,YAAc,YAKvB,MAAME,EAAcE,EAAM,WACxB,CAACM,EAAOC,IAAiB,CACvB,KAAM,CAAE,UAAAC,EAAW,SAAAM,EAAU,GAAGC,CAAa,EAAIT,EACjD,OACEN,EAAA,cAAC,EAAAU,KAAc,QAAd,CACE,GAAGK,EACJ,QAAS,GACT,IAAKR,EACL,aAAW,EAAAI,SAAW,WAAY,wBAAyB,iBAAkBH,CAAS,GAEtFR,EAAA,cAAC,QAAK,UAAU,kDAAkDc,CAAS,EAC3Ed,EAAA,cAAC,QAAK,UAAU,8DACbc,CACH,CACF,CAEJ,CACF,EACAhB,EAAY,YAAc,eAQ1B,MAAMH,EAAcK,EAAM,WACxB,CAACM,EAAOC,IAAiB,CACvB,KAAM,CAAE,UAAAC,EAAW,GAAGQ,CAAa,KAAI,gBAAaV,EAAO,gBAAc,EACzE,OACEN,EAAA,cAAC,EAAAU,KAAc,QAAd,CACE,GAAGM,EACJ,IAAKT,EACL,aAAW,EAAAI,SAAW,iBAAkBH,CAAS,EACnD,CAEJ,CACF,EACAb,EAAY,YAAc", "names": ["tabs_exports", "__export", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsList", "TabsRoot", "TabsTrigger", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_tabs_props", "import_extract_props", "import_margin_props", "props", "forwardedRef", "className", "rootProps", "TabsPrimitive", "classNames", "color", "listProps", "children", "triggerProps", "contentProps"]}