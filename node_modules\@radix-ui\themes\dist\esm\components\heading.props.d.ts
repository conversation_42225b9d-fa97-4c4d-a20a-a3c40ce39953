declare const headingPropDefs: {
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: ("gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky") | undefined;
    };
    wrap: {
        type: "enum";
        className: string;
        values: readonly ["wrap", "nowrap", "pretty", "balance"];
        responsive: true;
    };
    truncate: {
        type: "boolean";
        className: string;
    };
    trim: {
        type: "enum";
        className: string;
        values: readonly ["normal", "start", "end", "both"];
        responsive: true;
    };
    align: {
        type: "enum";
        className: string;
        values: readonly ["left", "center", "right"];
        responsive: true;
    };
    weight: {
        type: "enum";
        className: string;
        values: readonly ["light", "regular", "medium", "bold"];
        responsive: true;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4", "5", "6", "7", "8", "9"];
        default: "6";
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
    as: {
        type: "enum";
        values: readonly ["h1", "h2", "h3", "h4", "h5", "h6"];
        default: "h1";
    };
};
export { headingPropDefs };
//# sourceMappingURL=heading.props.d.ts.map