{"version": 3, "sources": ["../../../src/components/text.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Slot } from 'radix-ui';\n\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\nimport { textPropDefs } from './text.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\n\ntype TextElement = React.ElementRef<'span'>;\ntype TextOwnProps = GetPropDefTypes<typeof textPropDefs>;\ninterface CommonTextProps extends MarginProps, TextOwnProps {}\ntype TextSpanProps = { as?: 'span' } & ComponentPropsWithout<'span', RemovedProps>;\ntype TextDivProps = { as: 'div' } & ComponentPropsWithout<'div', RemovedProps>;\ntype TextLabelProps = { as: 'label' } & ComponentPropsWithout<'label', RemovedProps>;\ntype TextPProps = { as: 'p' } & ComponentPropsWithout<'p', RemovedProps>;\ntype TextProps = CommonTextProps & (TextSpanProps | TextDivProps | TextLabelProps | TextPProps);\n\nconst Text = React.forwardRef<TextElement, TextProps>((props, forwardedRef) => {\n  const {\n    children,\n    className,\n    asChild,\n    as: Tag = 'span',\n    color,\n    ...textProps\n  } = extractProps(props, textPropDefs, marginPropDefs);\n  return (\n    <Slot.Root\n      data-accent-color={color}\n      {...textProps}\n      ref={forwardedRef}\n      className={classNames('rt-Text', className)}\n    >\n      {asChild ? children : <Tag>{children}</Tag>}\n    </Slot.Root>\n  );\n});\nText.displayName = 'Text';\n\nexport { Text };\nexport type { TextProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,QAAAC,MAAY,WAErB,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,kBAAAC,MAAsB,2BAC/B,OAAS,gBAAAC,MAAoB,kBAe7B,MAAMC,EAAON,EAAM,WAAmC,CAACO,EAAOC,IAAiB,CAC7E,KAAM,CACJ,SAAAC,EACA,UAAAC,EACA,QAAAC,EACA,GAAIC,EAAM,OACV,MAAAC,EACA,GAAGC,CACL,EAAIX,EAAaI,EAAOF,EAAcD,CAAc,EACpD,OACEJ,EAAA,cAACE,EAAK,KAAL,CACC,oBAAmBW,EAClB,GAAGC,EACJ,IAAKN,EACL,UAAWP,EAAW,UAAWS,CAAS,GAEzCC,EAAUF,EAAWT,EAAA,cAACY,EAAA,KAAKH,CAAS,CACvC,CAEJ,CAAC,EACDH,EAAK,YAAc", "names": ["React", "classNames", "Slot", "extractProps", "marginPropDefs", "textPropDefs", "Text", "props", "forwardedRef", "children", "className", "<PERSON><PERSON><PERSON><PERSON>", "Tag", "color", "textProps"]}