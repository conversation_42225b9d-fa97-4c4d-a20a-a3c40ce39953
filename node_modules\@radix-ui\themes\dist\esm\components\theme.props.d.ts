import { asChildPropDef } from '../props/as-child.prop.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const themePropDefs: {
    /**
     * Whether to apply background color to the Theme element.
     *
     * Defaults to true for the root Theme and for Theme elements that
     * have an explicit light or dark appearance prop.
     */
    hasBackground: {
        type: "boolean";
        default: true;
    };
    /**
     * Sets the color scheme of the theme, typcially referred to as light and dark mode.
     *
     * @link
     * https://www.radix-ui.com/themes/docs/theme/dark-mode
     */
    appearance: {
        type: "enum";
        values: readonly ["inherit", "light", "dark"];
        default: "inherit";
    };
    /**
     * Selects one of the accent color options to use in the Theme.
     *
     * @link
     * https://www.radix-ui.com/themes/docs/theme/color
     */
    accentColor: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: "indigo";
    };
    /**
     * Selects one of the gray color options to use in the Theme.
     *
     * @link
     * https://www.radix-ui.com/themes/docs/theme/color
     */
    grayColor: {
        type: "enum";
        values: readonly ["auto", "gray", "mauve", "slate", "sage", "olive", "sand"];
        default: "auto";
    };
    /**
     * Controls whether to use a solid or translucent background color on panelled
     * elements such as Card or Table is solid or translucent.
     *
     * @link
     * https://www.radix-ui.com/themes/docs/theme/visual-style
     */
    panelBackground: {
        type: "enum";
        values: readonly ["solid", "translucent"];
        default: "translucent";
    };
    /**
     * Sets the default radius of the components.
     *
     * @link
     * https://www.radix-ui.com/themes/docs/theme/visual-style
     */
    radius: {
        type: "enum";
        values: readonly ["none", "small", "medium", "large", "full"];
        default: "medium";
    };
    /**
     * Sets a scaling multiplier for values like spacing, font sizes, line heights, etc. are scaled.
     *
     * @link
     * https://www.radix-ui.com/themes/docs/theme/layout
     */
    scaling: {
        type: "enum";
        values: readonly ["90%", "95%", "100%", "105%", "110%"];
        default: "100%";
    };
    asChild: {
        type: "boolean";
    };
};
type ThemeOwnProps = GetPropDefTypes<typeof themePropDefs & typeof asChildPropDef>;
export { themePropDefs };
export type { ThemeOwnProps };
//# sourceMappingURL=theme.props.d.ts.map