'use client';

import { useCallback } from 'react';
import { useLocalStorage } from './use-local-storage';
import type { UseUnitsReturn, Units, TemperatureUnit, WindUnit } from '@/types';

const DEFAULT_UNITS: Units = {
  temp: 'celsius',
  wind: 'kmh',
};

export function useUnits(): UseUnitsReturn {
  const { value: units, setValue: setUnitsValue } = useLocalStorage<Units>('units', DEFAULT_UNITS);

  const setUnits = useCallback((newUnits: Units) => {
    setUnitsValue(newUnits);
  }, [setUnitsValue]);

  const setTemperatureUnit = useCallback((unit: TemperatureUnit) => {
    setUnitsValue({
      ...units,
      temp: unit,
    });
  }, [units, setUnitsValue]);

  const setWindUnit = useCallback((unit: WindUnit) => {
    setUnitsValue({
      ...units,
      wind: unit,
    });
  }, [units, setUnitsValue]);

  return {
    units,
    setUnits,
    setTemperatureUnit,
    setWindUnit,
  };
}
