import { asChildPropDef } from '../props/as-child.prop.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const boxPropDefs: {
    /**
     * Sets the CSS **display** property.
     * Supports a subset of the corresponding CSS values and responsive objects.
     *
     * @example
     * display="inline-block"
     * display={{ sm: 'none', lg: 'block' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/display
     */
    display: {
        type: "enum";
        className: string;
        values: readonly ["none", "inline", "inline-block", "block", "contents"];
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
    /**
     * Controls whether to render **div** or **span**
     *
     * @example
     * as="div"
     * as="span"
     */
    as: {
        type: "enum";
        values: readonly ["div", "span"];
        default: "div";
    };
};
type BoxOwnProps = GetPropDefTypes<typeof boxPropDefs & typeof asChildPropDef>;
export { boxPropDefs };
export type { BoxOwnProps };
//# sourceMappingURL=box.props.d.ts.map