{"version": 3, "sources": ["../../../src/components/table.props.tsx"], "sourcesContent": ["import { paddingPropDefs } from '../props/padding.props.js';\nimport { widthPropDefs } from '../props/width.props.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\nconst variants = ['surface', 'ghost'] as const;\nconst layoutValues = ['auto', 'fixed'] as const;\n\nconst tableRootPropDefs = {\n  size: {\n    type: 'enum',\n    className: 'rt-r-size',\n    values: sizes,\n    default: '2',\n    responsive: true,\n  },\n  variant: {\n    type: 'enum',\n    className: 'rt-variant',\n    values: variants,\n    default: 'ghost',\n  },\n  layout: {\n    type: 'enum',\n    className: 'rt-r-tl',\n    values: layoutValues,\n    responsive: true,\n  },\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  variant: PropDef<(typeof variants)[number]>;\n  layout: PropDef<(typeof layoutValues)[number]>;\n};\n\nconst rowAlign = ['start', 'center', 'end', 'baseline'] as const;\n\nconst tableRowPropDefs = {\n  align: {\n    type: 'enum',\n    className: 'rt-r-va',\n    values: rowAlign,\n    parseValue: parseAlignValue,\n    responsive: true,\n  },\n} satisfies {\n  align: PropDef<(typeof rowAlign)[number]>;\n};\n\nfunction parseAlignValue(value: string) {\n  return {\n    baseline: 'baseline',\n    start: 'top',\n    center: 'middle',\n    end: 'bottom',\n  }[value];\n}\n\nconst justifyValues = ['start', 'center', 'end'] as const;\n\nconst tableCellPropDefs = {\n  justify: {\n    type: 'enum',\n    className: 'rt-r-ta',\n    values: justifyValues,\n    parseValue: parseJustifyValue,\n    responsive: true,\n  },\n  ...widthPropDefs,\n  ...paddingPropDefs,\n} satisfies {\n  justify: PropDef<(typeof justifyValues)[number]>;\n};\n\nfunction parseJustifyValue(value: string) {\n  return {\n    start: 'left',\n    center: 'center',\n    end: 'right',\n  }[value];\n}\n\nexport { tableRootPropDefs, tableRowPropDefs, tableCellPropDefs };\n"], "mappings": "AAAA,OAAS,mBAAAA,MAAuB,4BAChC,OAAS,iBAAAC,MAAqB,0BAI9B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EACtBC,EAAW,CAAC,UAAW,OAAO,EAC9BC,EAAe,CAAC,OAAQ,OAAO,EAE/BC,EAAoB,CACxB,KAAM,CACJ,KAAM,OACN,UAAW,YACX,OAAQH,EACR,QAAS,IACT,WAAY,EACd,EACA,QAAS,CACP,KAAM,OACN,UAAW,aACX,OAAQC,EACR,QAAS,OACX,EACA,OAAQ,CACN,KAAM,OACN,UAAW,UACX,OAAQC,EACR,WAAY,EACd,CACF,EAMME,EAAW,CAAC,QAAS,SAAU,MAAO,UAAU,EAEhDC,EAAmB,CACvB,MAAO,CACL,KAAM,OACN,UAAW,UACX,OAAQD,EACR,WAAYE,EACZ,WAAY,EACd,CACF,EAIA,SAASA,EAAgBC,EAAe,CACtC,MAAO,CACL,SAAU,WACV,MAAO,MACP,OAAQ,SACR,IAAK,QACP,EAAEA,CAAK,CACT,CAEA,MAAMC,EAAgB,CAAC,QAAS,SAAU,KAAK,EAEzCC,EAAoB,CACxB,QAAS,CACP,KAAM,OACN,UAAW,UACX,OAAQD,EACR,WAAYE,EACZ,WAAY,EACd,EACA,GAAGX,EACH,GAAGD,CACL,EAIA,SAASY,EAAkBH,EAAe,CACxC,MAAO,CACL,MAAO,OACP,OAAQ,SACR,IAAK,OACP,EAAEA,CAAK,CACT", "names": ["paddingPropDefs", "widthPropDefs", "sizes", "variants", "<PERSON><PERSON><PERSON><PERSON>", "tableRootPropDefs", "rowAlign", "tableRowPropDefs", "parseAlignValue", "value", "justifyValues", "tableCellPropDefs", "parseJustifyValue"]}