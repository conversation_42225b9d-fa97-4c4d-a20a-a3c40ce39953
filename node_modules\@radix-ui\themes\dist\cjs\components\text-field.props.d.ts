declare const textFieldRootPropDefs: {
    radius: {
        type: "enum";
        values: readonly ["none", "small", "medium", "large", "full"];
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: ("gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky") | undefined;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3"];
        default: "2";
        responsive: true;
    };
    variant: {
        type: "enum";
        className: string;
        values: readonly ["classic", "surface", "soft"];
        default: "surface";
    };
};
declare const textFieldSlotPropDefs: {
    gap: {
        type: "enum | string";
        className: string;
        customProperties: "--gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    px: {
        type: "enum | string";
        className: string;
        customProperties: ("--pl" | "--pr")[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    pl: {
        type: "enum | string";
        className: string;
        customProperties: "--pl"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    pr: {
        type: "enum | string";
        className: string;
        customProperties: "--pr"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: ("gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky") | undefined;
    };
    side: {
        type: "enum";
        values: readonly ["left", "right"];
    };
};
export { textFieldRootPropDefs, textFieldSlotPropDefs };
//# sourceMappingURL=text-field.props.d.ts.map