declare const baseCheckboxPropDefs: {
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: ("gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky") | undefined;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3"];
        default: "2";
        responsive: true;
    };
    variant: {
        type: "enum";
        className: string;
        values: readonly ["classic", "surface", "soft"];
        default: "surface";
    };
};
export { baseCheckboxPropDefs };
//# sourceMappingURL=base-checkbox.props.d.ts.map