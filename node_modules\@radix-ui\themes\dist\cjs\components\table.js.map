{"version": 3, "sources": ["../../../src/components/table.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\n\nimport { tableRootPropDefs, tableRowPropDefs, tableCellPropDefs } from './table.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { getResponsiveClassNames } from '../helpers/get-responsive-styles.js';\nimport { marginPropDefs } from '../props/margin.props.js';\nimport { ScrollArea } from './scroll-area.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TableRootElement = React.ElementRef<'div'>;\ntype TableRootOwnProps = GetPropDefTypes<typeof tableRootPropDefs>;\ninterface TableRootProps\n  extends ComponentPropsWithout<'div', RemovedProps>,\n    MarginProps,\n    TableRootOwnProps {}\nconst TableRoot = React.forwardRef<TableRootElement, TableRootProps>((props, forwardedRef) => {\n  const { layout: layoutPropDef, ...rootPropDefs } = tableRootPropDefs;\n  const { className, children, layout, ...rootProps } = extractProps(\n    props,\n    rootPropDefs,\n    marginPropDefs\n  );\n  const tableLayoutClassNames = getResponsiveClassNames({\n    value: layout,\n    className: tableRootPropDefs.layout.className,\n    propValues: tableRootPropDefs.layout.values,\n  });\n  return (\n    <div ref={forwardedRef} className={classNames('rt-TableRoot', className)} {...rootProps}>\n      <ScrollArea>\n        <table className={classNames('rt-TableRootTable', tableLayoutClassNames)}>{children}</table>\n      </ScrollArea>\n    </div>\n  );\n});\nTableRoot.displayName = 'Table.Root';\n\ntype TableHeaderElement = React.ElementRef<'thead'>;\ninterface TableHeaderProps extends ComponentPropsWithout<'thead', RemovedProps> {}\nconst TableHeader = React.forwardRef<TableHeaderElement, TableHeaderProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <thead {...props} ref={forwardedRef} className={classNames('rt-TableHeader', className)} />\n  )\n);\nTableHeader.displayName = 'Table.Header';\n\ntype TableBodyElement = React.ElementRef<'tbody'>;\ninterface TableBodyProps extends ComponentPropsWithout<'tbody', RemovedProps> {}\nconst TableBody = React.forwardRef<TableBodyElement, TableBodyProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <tbody {...props} ref={forwardedRef} className={classNames('rt-TableBody', className)} />\n  )\n);\nTableBody.displayName = 'Table.Body';\n\ntype TableRowElement = React.ElementRef<'tr'>;\ntype TableRowOwnProps = GetPropDefTypes<typeof tableRowPropDefs>;\ninterface TableRowProps extends ComponentPropsWithout<'tr', RemovedProps>, TableRowOwnProps {}\nconst TableRow = React.forwardRef<TableRowElement, TableRowProps>((props, forwardedRef) => {\n  const { className, ...rowProps } = extractProps(props, tableRowPropDefs);\n  return <tr {...rowProps} ref={forwardedRef} className={classNames('rt-TableRow', className)} />;\n});\nTableRow.displayName = 'Table.Row';\n\ntype TableCellElement = React.ElementRef<'td'>;\ntype TableCellOwnProps = GetPropDefTypes<typeof tableCellPropDefs>;\ninterface TableCellProps\n  extends ComponentPropsWithout<'td', RemovedProps | 'width'>,\n    TableCellOwnProps {}\nconst TableCell = React.forwardRef<TableCellElement, TableCellProps>((props, forwardedRef) => {\n  const { className, ...cellProps } = extractProps(props, tableCellPropDefs);\n  return <td className={classNames('rt-TableCell', className)} ref={forwardedRef} {...cellProps} />;\n});\nTableCell.displayName = 'Table.Cell';\n\ntype TableColumnHeaderCellElement = React.ElementRef<'th'>;\ninterface TableColumnHeaderCellProps\n  extends ComponentPropsWithout<'th', RemovedProps>,\n    TableCellOwnProps {}\nconst TableColumnHeaderCell = React.forwardRef<\n  TableColumnHeaderCellElement,\n  TableColumnHeaderCellProps\n>((props, forwardedRef) => {\n  const { className, ...cellProps } = extractProps(props, tableCellPropDefs);\n  return (\n    <th\n      className={classNames('rt-TableCell', 'rt-TableColumnHeaderCell', className)}\n      scope=\"col\"\n      ref={forwardedRef}\n      {...cellProps}\n    />\n  );\n});\nTableColumnHeaderCell.displayName = 'Table.ColumnHeaderCell';\n\ntype TableRowHeaderCellElement = React.ElementRef<'th'>;\ninterface TableRowHeaderCellProps\n  extends ComponentPropsWithout<'th', RemovedProps>,\n    TableCellOwnProps {}\nconst TableRowHeaderCell = React.forwardRef<TableRowHeaderCellElement, TableRowHeaderCellProps>(\n  (props, forwardedRef) => {\n    const { className, ...cellProps } = extractProps(props, tableCellPropDefs);\n    return (\n      <th\n        className={classNames('rt-TableCell', 'rt-TableRowHeaderCell', className)}\n        scope=\"row\"\n        ref={forwardedRef}\n        {...cellProps}\n      />\n    );\n  }\n);\nTableRowHeaderCell.displayName = 'Table.RowHeaderCell';\n\nexport {\n  TableRoot as Root,\n  TableHeader as Header,\n  TableBody as Body,\n  TableRow as Row,\n  TableCell as Cell,\n  TableColumnHeaderCell as ColumnHeaderCell,\n  TableRowHeaderCell as RowHeaderCell,\n};\n\nexport type {\n  TableRootProps as RootProps,\n  TableHeaderProps as HeaderProps,\n  TableBodyProps as BodyProps,\n  TableRowProps as RowProps,\n  TableCellProps as CellProps,\n  TableColumnHeaderCellProps as ColumnHeaderCellProps,\n  TableRowHeaderCellProps as RowHeaderCellProps,\n};\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,UAAAE,EAAA,SAAAC,EAAA,qBAAAC,EAAA,WAAAC,EAAA,SAAAC,EAAA,QAAAC,EAAA,kBAAAC,IAAA,eAAAC,EAAAT,GAAA,IAAAU,EAAuB,oBACvBC,EAAuB,yBAEvBC,EAAuE,4BACvEC,EAA6B,uCAC7BC,EAAwC,+CACxCC,EAA+B,oCAC/BC,EAA2B,4BAY3B,MAAMV,EAAYI,EAAM,WAA6C,CAACO,EAAOC,IAAiB,CAC5F,KAAM,CAAE,OAAQC,EAAe,GAAGC,CAAa,EAAI,oBAC7C,CAAE,UAAAC,EAAW,SAAAC,EAAU,OAAAC,EAAQ,GAAGC,CAAU,KAAI,gBACpDP,EACAG,EACA,gBACF,EACMK,KAAwB,2BAAwB,CACpD,MAAOF,EACP,UAAW,oBAAkB,OAAO,UACpC,WAAY,oBAAkB,OAAO,MACvC,CAAC,EACD,OACEb,EAAA,cAAC,OAAI,IAAKQ,EAAc,aAAW,EAAAQ,SAAW,eAAgBL,CAAS,EAAI,GAAGG,GAC5Ed,EAAA,cAAC,kBACCA,EAAA,cAAC,SAAM,aAAW,EAAAgB,SAAW,oBAAqBD,CAAqB,GAAIH,CAAS,CACtF,CACF,CAEJ,CAAC,EACDhB,EAAU,YAAc,aAIxB,MAAMD,EAAcK,EAAM,WACxB,CAAC,CAAE,UAAAW,EAAW,GAAGJ,CAAM,EAAGC,IACxBR,EAAA,cAAC,SAAO,GAAGO,EAAO,IAAKC,EAAc,aAAW,EAAAQ,SAAW,iBAAkBL,CAAS,EAAG,CAE7F,EACAhB,EAAY,YAAc,eAI1B,MAAMH,EAAYQ,EAAM,WACtB,CAAC,CAAE,UAAAW,EAAW,GAAGJ,CAAM,EAAGC,IACxBR,EAAA,cAAC,SAAO,GAAGO,EAAO,IAAKC,EAAc,aAAW,EAAAQ,SAAW,eAAgBL,CAAS,EAAG,CAE3F,EACAnB,EAAU,YAAc,aAKxB,MAAMK,EAAWG,EAAM,WAA2C,CAACO,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAG,EAAW,GAAGM,CAAS,KAAI,gBAAaV,EAAO,kBAAgB,EACvE,OAAOP,EAAA,cAAC,MAAI,GAAGiB,EAAU,IAAKT,EAAc,aAAW,EAAAQ,SAAW,cAAeL,CAAS,EAAG,CAC/F,CAAC,EACDd,EAAS,YAAc,YAOvB,MAAMJ,EAAYO,EAAM,WAA6C,CAACO,EAAOC,IAAiB,CAC5F,KAAM,CAAE,UAAAG,EAAW,GAAGO,CAAU,KAAI,gBAAaX,EAAO,mBAAiB,EACzE,OAAOP,EAAA,cAAC,MAAG,aAAW,EAAAgB,SAAW,eAAgBL,CAAS,EAAG,IAAKH,EAAe,GAAGU,EAAW,CACjG,CAAC,EACDzB,EAAU,YAAc,aAMxB,MAAMC,EAAwBM,EAAM,WAGlC,CAACO,EAAOC,IAAiB,CACzB,KAAM,CAAE,UAAAG,EAAW,GAAGO,CAAU,KAAI,gBAAaX,EAAO,mBAAiB,EACzE,OACEP,EAAA,cAAC,MACC,aAAW,EAAAgB,SAAW,eAAgB,2BAA4BL,CAAS,EAC3E,MAAM,MACN,IAAKH,EACJ,GAAGU,EACN,CAEJ,CAAC,EACDxB,EAAsB,YAAc,yBAMpC,MAAMI,EAAqBE,EAAM,WAC/B,CAACO,EAAOC,IAAiB,CACvB,KAAM,CAAE,UAAAG,EAAW,GAAGO,CAAU,KAAI,gBAAaX,EAAO,mBAAiB,EACzE,OACEP,EAAA,cAAC,MACC,aAAW,EAAAgB,SAAW,eAAgB,wBAAyBL,CAAS,EACxE,MAAM,MACN,IAAKH,EACJ,GAAGU,EACN,CAEJ,CACF,EACApB,EAAmB,YAAc", "names": ["table_exports", "__export", "TableBody", "TableCell", "TableColumnHeaderCell", "TableHeader", "TableRoot", "TableRow", "TableRowHeaderCell", "__toCommonJS", "React", "import_classnames", "import_table_props", "import_extract_props", "import_get_responsive_styles", "import_margin_props", "import_scroll_area", "props", "forwardedRef", "layoutPropDef", "rootPropDefs", "className", "children", "layout", "rootProps", "tableLayoutClassNames", "classNames", "rowProps", "cellProps"]}