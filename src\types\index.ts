// Export all weather-related types
export type {
  Location,
  WeatherCurrent,
  WeatherDaily,
  WeatherHourly,
  WeatherData,
  AQIHourly,
  AQIData,
  AQICategory,
  PressureData,
  TemperatureUnit,
  WindUnit,
  Units,
  WeatherFetchOptions,
  GeocodingOptions,
  PostalGeocodingOptions,
  WeatherCode,
  WeatherCodeMapping,
} from './weather';

// Export all component prop types
export type {
  BaseComponentProps,
  SearchBarProps,
  FavoritesListProps,
  UnitsSelectorProps,
  CurrentWeatherProps,
  WeatherForecastProps,
  HourlyForecastProps,
  AirQualityProps,
  WeatherCardProps,
  FavoriteButtonProps,
  LoadingStateProps,
  ErrorStateProps,
  WeatherPageProps,
} from './components';

// Export all hook types
export type {
  UseWeatherDataReturn,
  UseGeolocationReturn,
  UseFavoritesReturn,
  UseLocalStorageReturn,
  UseSearchReturn,
  UseUnitsReturn,
  UseDebounceReturn,
  LocalStorageKey,
  WeatherError,
  APIResponse,
} from './hooks';
