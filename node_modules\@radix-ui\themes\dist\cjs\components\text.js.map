{"version": 3, "sources": ["../../../src/components/text.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Slot } from 'radix-ui';\n\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\nimport { textPropDefs } from './text.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\n\ntype TextElement = React.ElementRef<'span'>;\ntype TextOwnProps = GetPropDefTypes<typeof textPropDefs>;\ninterface CommonTextProps extends MarginProps, TextOwnProps {}\ntype TextSpanProps = { as?: 'span' } & ComponentPropsWithout<'span', RemovedProps>;\ntype TextDivProps = { as: 'div' } & ComponentPropsWithout<'div', RemovedProps>;\ntype TextLabelProps = { as: 'label' } & ComponentPropsWithout<'label', RemovedProps>;\ntype TextPProps = { as: 'p' } & ComponentPropsWithout<'p', RemovedProps>;\ntype TextProps = CommonTextProps & (TextSpanProps | TextDivProps | TextLabelProps | TextPProps);\n\nconst Text = React.forwardRef<TextElement, TextProps>((props, forwardedRef) => {\n  const {\n    children,\n    className,\n    asChild,\n    as: Tag = 'span',\n    color,\n    ...textProps\n  } = extractProps(props, textPropDefs, marginPropDefs);\n  return (\n    <Slot.Root\n      data-accent-color={color}\n      {...textProps}\n      ref={forwardedRef}\n      className={classNames('rt-Text', className)}\n    >\n      {asChild ? children : <Tag>{children}</Tag>}\n    </Slot.Root>\n  );\n});\nText.displayName = 'Text';\n\nexport { Text };\nexport type { TextProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,UAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,oBACvBC,EAAuB,yBACvBC,EAAqB,oBAErBC,EAA6B,uCAC7BC,EAA+B,oCAC/BC,EAA6B,2BAe7B,MAAMP,EAAOE,EAAM,WAAmC,CAACM,EAAOC,IAAiB,CAC7E,KAAM,CACJ,SAAAC,EACA,UAAAC,EACA,QAAAC,EACA,GAAIC,EAAM,OACV,MAAAC,EACA,GAAGC,CACL,KAAI,gBAAaP,EAAO,eAAc,gBAAc,EACpD,OACEN,EAAA,cAAC,OAAK,KAAL,CACC,oBAAmBY,EAClB,GAAGC,EACJ,IAAKN,EACL,aAAW,EAAAO,SAAW,UAAWL,CAAS,GAEzCC,EAAUF,EAAWR,EAAA,cAACW,EAAA,KAAKH,CAAS,CACvC,CAEJ,CAAC,EACDV,EAAK,YAAc", "names": ["text_exports", "__export", "Text", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_extract_props", "import_margin_props", "import_text_props", "props", "forwardedRef", "children", "className", "<PERSON><PERSON><PERSON><PERSON>", "Tag", "color", "textProps", "classNames"]}