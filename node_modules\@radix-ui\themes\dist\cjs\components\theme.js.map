{"version": 3, "sources": ["../../../src/components/theme.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Direction, Slot, Tooltip as TooltipPrimitive } from 'radix-ui';\n\nimport { getMatchingGrayColor } from '../helpers/get-matching-gray-color.js';\nimport { themePropDefs } from './theme.props.js';\n\nimport type { ThemeOwnProps } from './theme.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\n\nconst noop = () => {};\n\ntype ThemeAppearance = (typeof themePropDefs.appearance.values)[number];\ntype ThemeAccentColor = (typeof themePropDefs.accentColor.values)[number];\ntype ThemeGrayColor = (typeof themePropDefs.grayColor.values)[number];\ntype ThemePanelBackground = (typeof themePropDefs.panelBackground.values)[number];\ntype ThemeRadius = (typeof themePropDefs.radius.values)[number];\ntype ThemeScaling = (typeof themePropDefs.scaling.values)[number];\n\ninterface ThemeChangeHandlers {\n  onAppearanceChange: (appearance: ThemeAppearance) => void;\n  onAccentColorChange: (accentColor: ThemeAccentColor) => void;\n  onGrayColorChange: (grayColor: ThemeGrayColor) => void;\n  onPanelBackgroundChange: (panelBackground: ThemePanelBackground) => void;\n  onRadiusChange: (radius: ThemeRadius) => void;\n  onScalingChange: (scaling: ThemeScaling) => void;\n}\n\ninterface ThemeContextValue extends ThemeChangeHandlers {\n  appearance: ThemeAppearance;\n  accentColor: ThemeAccentColor;\n  grayColor: ThemeGrayColor;\n  resolvedGrayColor: ThemeGrayColor;\n  panelBackground: ThemePanelBackground;\n  radius: ThemeRadius;\n  scaling: ThemeScaling;\n}\nconst ThemeContext = React.createContext<ThemeContextValue | undefined>(undefined);\n\nfunction useThemeContext() {\n  const context = React.useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('`useThemeContext` must be used within a `Theme`');\n  }\n  return context;\n}\n\ninterface ThemeProps extends ThemeImplPublicProps {}\nconst Theme = React.forwardRef<ThemeImplElement, ThemeProps>((props, forwardedRef) => {\n  const context = React.useContext(ThemeContext);\n  const isRoot = context === undefined;\n  if (isRoot) {\n    return (\n      <TooltipPrimitive.Provider delayDuration={200}>\n        <Direction.Provider dir=\"ltr\">\n          <ThemeRoot {...props} ref={forwardedRef} />\n        </Direction.Provider>\n      </TooltipPrimitive.Provider>\n    );\n  }\n  return <ThemeImpl {...props} ref={forwardedRef} />;\n});\nTheme.displayName = 'Theme';\n\nconst ThemeRoot = React.forwardRef<ThemeImplElement, ThemeImplPublicProps>(\n  (props, forwardedRef) => {\n    const {\n      appearance: appearanceProp = themePropDefs.appearance.default,\n      accentColor: accentColorProp = themePropDefs.accentColor.default,\n      grayColor: grayColorProp = themePropDefs.grayColor.default,\n      panelBackground: panelBackgroundProp = themePropDefs.panelBackground.default,\n      radius: radiusProp = themePropDefs.radius.default,\n      scaling: scalingProp = themePropDefs.scaling.default,\n      hasBackground = themePropDefs.hasBackground.default,\n      ...rootProps\n    } = props;\n    const [appearance, setAppearance] = React.useState(appearanceProp);\n    React.useEffect(() => setAppearance(appearanceProp), [appearanceProp]);\n\n    const [accentColor, setAccentColor] = React.useState(accentColorProp);\n    React.useEffect(() => setAccentColor(accentColorProp), [accentColorProp]);\n\n    const [grayColor, setGrayColor] = React.useState(grayColorProp);\n    React.useEffect(() => setGrayColor(grayColorProp), [grayColorProp]);\n\n    const [panelBackground, setPanelBackground] = React.useState(panelBackgroundProp);\n    React.useEffect(() => setPanelBackground(panelBackgroundProp), [panelBackgroundProp]);\n\n    const [radius, setRadius] = React.useState(radiusProp);\n    React.useEffect(() => setRadius(radiusProp), [radiusProp]);\n\n    const [scaling, setScaling] = React.useState(scalingProp);\n    React.useEffect(() => setScaling(scalingProp), [scalingProp]);\n\n    return (\n      <ThemeImpl\n        {...rootProps}\n        ref={forwardedRef}\n        isRoot\n        hasBackground={hasBackground}\n        //\n        appearance={appearance}\n        accentColor={accentColor}\n        grayColor={grayColor}\n        panelBackground={panelBackground}\n        radius={radius}\n        scaling={scaling}\n        //\n        onAppearanceChange={setAppearance}\n        onAccentColorChange={setAccentColor}\n        onGrayColorChange={setGrayColor}\n        onPanelBackgroundChange={setPanelBackground}\n        onRadiusChange={setRadius}\n        onScalingChange={setScaling}\n      />\n    );\n  }\n);\nThemeRoot.displayName = 'ThemeRoot';\n\ntype ThemeImplElement = React.ElementRef<'div'>;\ninterface ThemeImplProps extends ThemeImplPublicProps, ThemeImplPrivateProps {}\ninterface ThemeImplPublicProps\n  extends ComponentPropsWithout<'div', RemovedProps | 'dir'>,\n    ThemeOwnProps {}\ninterface ThemeImplPrivateProps extends Partial<ThemeChangeHandlers> {\n  isRoot?: boolean;\n}\nconst ThemeImpl = React.forwardRef<ThemeImplElement, ThemeImplProps>((props, forwardedRef) => {\n  const context = React.useContext(ThemeContext);\n  const {\n    asChild,\n    isRoot,\n    hasBackground: hasBackgroundProp,\n    //\n    appearance = context?.appearance ?? themePropDefs.appearance.default,\n    accentColor = context?.accentColor ?? themePropDefs.accentColor.default,\n    grayColor = context?.resolvedGrayColor ?? themePropDefs.grayColor.default,\n    panelBackground = context?.panelBackground ?? themePropDefs.panelBackground.default,\n    radius = context?.radius ?? themePropDefs.radius.default,\n    scaling = context?.scaling ?? themePropDefs.scaling.default,\n    //\n    onAppearanceChange = noop,\n    onAccentColorChange = noop,\n    onGrayColorChange = noop,\n    onPanelBackgroundChange = noop,\n    onRadiusChange = noop,\n    onScalingChange = noop,\n    //\n    ...themeProps\n  } = props;\n  const Comp = asChild ? Slot.Root : 'div';\n  const resolvedGrayColor = grayColor === 'auto' ? getMatchingGrayColor(accentColor) : grayColor;\n  const isExplicitAppearance = props.appearance === 'light' || props.appearance === 'dark';\n  const hasBackground =\n    hasBackgroundProp === undefined ? isRoot || isExplicitAppearance : hasBackgroundProp;\n  return (\n    <ThemeContext.Provider\n      value={React.useMemo(\n        () => ({\n          appearance,\n          accentColor,\n          grayColor,\n          resolvedGrayColor,\n          panelBackground,\n          radius,\n          scaling,\n          //\n          onAppearanceChange,\n          onAccentColorChange,\n          onGrayColorChange,\n          onPanelBackgroundChange,\n          onRadiusChange,\n          onScalingChange,\n        }),\n        [\n          appearance,\n          accentColor,\n          grayColor,\n          resolvedGrayColor,\n          panelBackground,\n          radius,\n          scaling,\n          //\n          onAppearanceChange,\n          onAccentColorChange,\n          onGrayColorChange,\n          onPanelBackgroundChange,\n          onRadiusChange,\n          onScalingChange,\n        ]\n      )}\n    >\n      <Comp\n        data-is-root-theme={isRoot ? 'true' : 'false'}\n        data-accent-color={accentColor}\n        data-gray-color={resolvedGrayColor}\n        // for nested `Theme` background\n        data-has-background={hasBackground ? 'true' : 'false'}\n        data-panel-background={panelBackground}\n        data-radius={radius}\n        data-scaling={scaling}\n        ref={forwardedRef}\n        {...themeProps}\n        className={classNames(\n          'radix-themes',\n          {\n            light: appearance === 'light',\n            dark: appearance === 'dark',\n          },\n          themeProps.className\n        )}\n      />\n    </ThemeContext.Provider>\n  );\n});\nThemeImpl.displayName = 'ThemeImpl';\n\nexport { Theme, ThemeContext, useThemeContext };\nexport type { ThemeProps };\n"], "mappings": "ukBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,WAAAE,EAAA,iBAAAC,EAAA,oBAAAC,IAAA,eAAAC,EAAAL,GAEA,IAAAM,EAAuB,oBACvBC,EAAuB,yBACvBC,EAA6D,oBAE7DC,EAAqC,iDACrCC,EAA8B,4BAK9B,MAAMC,EAAO,IAAM,CAAC,EA2BdR,EAAeG,EAAM,cAA6C,MAAS,EAEjF,SAASF,GAAkB,CACzB,MAAMQ,EAAUN,EAAM,WAAWH,CAAY,EAC7C,GAAIS,IAAY,OACd,MAAM,IAAI,MAAM,iDAAiD,EAEnE,OAAOA,CACT,CAGA,MAAMV,EAAQI,EAAM,WAAyC,CAACO,EAAOC,IACnDR,EAAM,WAAWH,CAAY,IAClB,OAGvBG,EAAA,cAAC,EAAAS,QAAiB,SAAjB,CAA0B,cAAe,KACxCT,EAAA,cAAC,YAAU,SAAV,CAAmB,IAAI,OACtBA,EAAA,cAACU,EAAA,CAAW,GAAGH,EAAO,IAAKC,EAAc,CAC3C,CACF,EAGGR,EAAA,cAACW,EAAA,CAAW,GAAGJ,EAAO,IAAKC,EAAc,CACjD,EACDZ,EAAM,YAAc,QAEpB,MAAMc,EAAYV,EAAM,WACtB,CAACO,EAAOC,IAAiB,CACvB,KAAM,CACJ,WAAYI,EAAiB,gBAAc,WAAW,QACtD,YAAaC,EAAkB,gBAAc,YAAY,QACzD,UAAWC,EAAgB,gBAAc,UAAU,QACnD,gBAAiBC,EAAsB,gBAAc,gBAAgB,QACrE,OAAQC,EAAa,gBAAc,OAAO,QAC1C,QAASC,EAAc,gBAAc,QAAQ,QAC7C,cAAAC,EAAgB,gBAAc,cAAc,QAC5C,GAAGC,CACL,EAAIZ,EACE,CAACa,EAAYC,CAAa,EAAIrB,EAAM,SAASY,CAAc,EACjEZ,EAAM,UAAU,IAAMqB,EAAcT,CAAc,EAAG,CAACA,CAAc,CAAC,EAErE,KAAM,CAACU,EAAaC,CAAc,EAAIvB,EAAM,SAASa,CAAe,EACpEb,EAAM,UAAU,IAAMuB,EAAeV,CAAe,EAAG,CAACA,CAAe,CAAC,EAExE,KAAM,CAACW,EAAWC,CAAY,EAAIzB,EAAM,SAASc,CAAa,EAC9Dd,EAAM,UAAU,IAAMyB,EAAaX,CAAa,EAAG,CAACA,CAAa,CAAC,EAElE,KAAM,CAACY,EAAiBC,CAAkB,EAAI3B,EAAM,SAASe,CAAmB,EAChFf,EAAM,UAAU,IAAM2B,EAAmBZ,CAAmB,EAAG,CAACA,CAAmB,CAAC,EAEpF,KAAM,CAACa,EAAQC,CAAS,EAAI7B,EAAM,SAASgB,CAAU,EACrDhB,EAAM,UAAU,IAAM6B,EAAUb,CAAU,EAAG,CAACA,CAAU,CAAC,EAEzD,KAAM,CAACc,EAASC,CAAU,EAAI/B,EAAM,SAASiB,CAAW,EACxD,OAAAjB,EAAM,UAAU,IAAM+B,EAAWd,CAAW,EAAG,CAACA,CAAW,CAAC,EAG1DjB,EAAA,cAACW,EAAA,CACE,GAAGQ,EACJ,IAAKX,EACL,OAAM,GACN,cAAeU,EAEf,WAAYE,EACZ,YAAaE,EACb,UAAWE,EACX,gBAAiBE,EACjB,OAAQE,EACR,QAASE,EAET,mBAAoBT,EACpB,oBAAqBE,EACrB,kBAAmBE,EACnB,wBAAyBE,EACzB,eAAgBE,EAChB,gBAAiBE,EACnB,CAEJ,CACF,EACArB,EAAU,YAAc,YAUxB,MAAMC,EAAYX,EAAM,WAA6C,CAACO,EAAOC,IAAiB,CAC5F,MAAMF,EAAUN,EAAM,WAAWH,CAAY,EACvC,CACJ,QAAAmC,EACA,OAAAC,EACA,cAAeC,EAEf,WAAAd,EAAad,GAAS,YAAc,gBAAc,WAAW,QAC7D,YAAAgB,EAAchB,GAAS,aAAe,gBAAc,YAAY,QAChE,UAAAkB,EAAYlB,GAAS,mBAAqB,gBAAc,UAAU,QAClE,gBAAAoB,EAAkBpB,GAAS,iBAAmB,gBAAc,gBAAgB,QAC5E,OAAAsB,EAAStB,GAAS,QAAU,gBAAc,OAAO,QACjD,QAAAwB,EAAUxB,GAAS,SAAW,gBAAc,QAAQ,QAEpD,mBAAA6B,EAAqB9B,EACrB,oBAAA+B,EAAsB/B,EACtB,kBAAAgC,EAAoBhC,EACpB,wBAAAiC,EAA0BjC,EAC1B,eAAAkC,EAAiBlC,EACjB,gBAAAmC,EAAkBnC,EAElB,GAAGoC,CACL,EAAIlC,EACEmC,EAAOV,EAAU,OAAK,KAAO,MAC7BW,EAAoBnB,IAAc,UAAS,wBAAqBF,CAAW,EAAIE,EAC/EoB,EAAuBrC,EAAM,aAAe,SAAWA,EAAM,aAAe,OAC5EW,EACJgB,IAAsB,OAAYD,GAAUW,EAAuBV,EACrE,OACElC,EAAA,cAACH,EAAa,SAAb,CACC,MAAOG,EAAM,QACX,KAAO,CACL,WAAAoB,EACA,YAAAE,EACA,UAAAE,EACA,kBAAAmB,EACA,gBAAAjB,EACA,OAAAE,EACA,QAAAE,EAEA,mBAAAK,EACA,oBAAAC,EACA,kBAAAC,EACA,wBAAAC,EACA,eAAAC,EACA,gBAAAC,CACF,GACA,CACEpB,EACAE,EACAE,EACAmB,EACAjB,EACAE,EACAE,EAEAK,EACAC,EACAC,EACAC,EACAC,EACAC,CACF,CACF,GAEAxC,EAAA,cAAC0C,EAAA,CACC,qBAAoBT,EAAS,OAAS,QACtC,oBAAmBX,EACnB,kBAAiBqB,EAEjB,sBAAqBzB,EAAgB,OAAS,QAC9C,wBAAuBQ,EACvB,cAAaE,EACb,eAAcE,EACd,IAAKtB,EACJ,GAAGiC,EACJ,aAAW,EAAAI,SACT,eACA,CACE,MAAOzB,IAAe,QACtB,KAAMA,IAAe,MACvB,EACAqB,EAAW,SACb,EACF,CACF,CAEJ,CAAC,EACD9B,EAAU,YAAc", "names": ["theme_exports", "__export", "Theme", "ThemeContext", "useThemeContext", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_get_matching_gray_color", "import_theme_props", "noop", "context", "props", "forwardedRef", "TooltipPrimitive", "ThemeRoot", "ThemeImpl", "appearanceProp", "accentColorProp", "grayColorProp", "panelBackgroundProp", "radiusProp", "scalingProp", "hasBackground", "rootProps", "appearance", "set<PERSON><PERSON><PERSON>ce", "accentColor", "setAccentColor", "grayColor", "setGrayColor", "panelBackground", "setPanelBackground", "radius", "setRadius", "scaling", "setScaling", "<PERSON><PERSON><PERSON><PERSON>", "isRoot", "hasBackgroundProp", "onAppearanceChange", "onAccentColorChange", "onGrayColorChange", "onPanelBackgroundChange", "onRadiusChange", "onScalingChange", "themeProps", "Comp", "resolvedGrayColor", "isExplicitAppearance", "classNames"]}