declare const baseTabListPropDefs: {
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: ("gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky") | undefined;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2"];
        default: "2";
        responsive: true;
    };
    wrap: {
        type: "enum";
        className: string;
        values: readonly ["nowrap", "wrap", "wrap-reverse"];
        responsive: true;
    };
    justify: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end"];
        responsive: true;
    };
};
export { baseTabListPropDefs };
//# sourceMappingURL=base-tab-list.props.d.ts.map