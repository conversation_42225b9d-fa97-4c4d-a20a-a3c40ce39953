{"version": 3, "sources": ["../../../src/components/table.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\n\nimport { tableRootPropDefs, tableRowPropDefs, tableCellPropDefs } from './table.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { getResponsiveClassNames } from '../helpers/get-responsive-styles.js';\nimport { marginPropDefs } from '../props/margin.props.js';\nimport { ScrollArea } from './scroll-area.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TableRootElement = React.ElementRef<'div'>;\ntype TableRootOwnProps = GetPropDefTypes<typeof tableRootPropDefs>;\ninterface TableRootProps\n  extends ComponentPropsWithout<'div', RemovedProps>,\n    MarginProps,\n    TableRootOwnProps {}\nconst TableRoot = React.forwardRef<TableRootElement, TableRootProps>((props, forwardedRef) => {\n  const { layout: layoutPropDef, ...rootPropDefs } = tableRootPropDefs;\n  const { className, children, layout, ...rootProps } = extractProps(\n    props,\n    rootPropDefs,\n    marginPropDefs\n  );\n  const tableLayoutClassNames = getResponsiveClassNames({\n    value: layout,\n    className: tableRootPropDefs.layout.className,\n    propValues: tableRootPropDefs.layout.values,\n  });\n  return (\n    <div ref={forwardedRef} className={classNames('rt-TableRoot', className)} {...rootProps}>\n      <ScrollArea>\n        <table className={classNames('rt-TableRootTable', tableLayoutClassNames)}>{children}</table>\n      </ScrollArea>\n    </div>\n  );\n});\nTableRoot.displayName = 'Table.Root';\n\ntype TableHeaderElement = React.ElementRef<'thead'>;\ninterface TableHeaderProps extends ComponentPropsWithout<'thead', RemovedProps> {}\nconst TableHeader = React.forwardRef<TableHeaderElement, TableHeaderProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <thead {...props} ref={forwardedRef} className={classNames('rt-TableHeader', className)} />\n  )\n);\nTableHeader.displayName = 'Table.Header';\n\ntype TableBodyElement = React.ElementRef<'tbody'>;\ninterface TableBodyProps extends ComponentPropsWithout<'tbody', RemovedProps> {}\nconst TableBody = React.forwardRef<TableBodyElement, TableBodyProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <tbody {...props} ref={forwardedRef} className={classNames('rt-TableBody', className)} />\n  )\n);\nTableBody.displayName = 'Table.Body';\n\ntype TableRowElement = React.ElementRef<'tr'>;\ntype TableRowOwnProps = GetPropDefTypes<typeof tableRowPropDefs>;\ninterface TableRowProps extends ComponentPropsWithout<'tr', RemovedProps>, TableRowOwnProps {}\nconst TableRow = React.forwardRef<TableRowElement, TableRowProps>((props, forwardedRef) => {\n  const { className, ...rowProps } = extractProps(props, tableRowPropDefs);\n  return <tr {...rowProps} ref={forwardedRef} className={classNames('rt-TableRow', className)} />;\n});\nTableRow.displayName = 'Table.Row';\n\ntype TableCellElement = React.ElementRef<'td'>;\ntype TableCellOwnProps = GetPropDefTypes<typeof tableCellPropDefs>;\ninterface TableCellProps\n  extends ComponentPropsWithout<'td', RemovedProps | 'width'>,\n    TableCellOwnProps {}\nconst TableCell = React.forwardRef<TableCellElement, TableCellProps>((props, forwardedRef) => {\n  const { className, ...cellProps } = extractProps(props, tableCellPropDefs);\n  return <td className={classNames('rt-TableCell', className)} ref={forwardedRef} {...cellProps} />;\n});\nTableCell.displayName = 'Table.Cell';\n\ntype TableColumnHeaderCellElement = React.ElementRef<'th'>;\ninterface TableColumnHeaderCellProps\n  extends ComponentPropsWithout<'th', RemovedProps>,\n    TableCellOwnProps {}\nconst TableColumnHeaderCell = React.forwardRef<\n  TableColumnHeaderCellElement,\n  TableColumnHeaderCellProps\n>((props, forwardedRef) => {\n  const { className, ...cellProps } = extractProps(props, tableCellPropDefs);\n  return (\n    <th\n      className={classNames('rt-TableCell', 'rt-TableColumnHeaderCell', className)}\n      scope=\"col\"\n      ref={forwardedRef}\n      {...cellProps}\n    />\n  );\n});\nTableColumnHeaderCell.displayName = 'Table.ColumnHeaderCell';\n\ntype TableRowHeaderCellElement = React.ElementRef<'th'>;\ninterface TableRowHeaderCellProps\n  extends ComponentPropsWithout<'th', RemovedProps>,\n    TableCellOwnProps {}\nconst TableRowHeaderCell = React.forwardRef<TableRowHeaderCellElement, TableRowHeaderCellProps>(\n  (props, forwardedRef) => {\n    const { className, ...cellProps } = extractProps(props, tableCellPropDefs);\n    return (\n      <th\n        className={classNames('rt-TableCell', 'rt-TableRowHeaderCell', className)}\n        scope=\"row\"\n        ref={forwardedRef}\n        {...cellProps}\n      />\n    );\n  }\n);\nTableRowHeaderCell.displayName = 'Table.RowHeaderCell';\n\nexport {\n  TableRoot as Root,\n  TableHeader as Header,\n  TableBody as Body,\n  TableRow as Row,\n  TableCell as Cell,\n  TableColumnHeaderCell as ColumnHeaderCell,\n  TableRowHeaderCell as RowHeaderCell,\n};\n\nexport type {\n  TableRootProps as RootProps,\n  TableHeaderProps as HeaderProps,\n  TableBodyProps as BodyProps,\n  TableRowProps as RowProps,\n  TableCellProps as CellProps,\n  TableColumnHeaderCellProps as ColumnHeaderCellProps,\n  TableRowHeaderCellProps as RowHeaderCellProps,\n};\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aAEvB,OAAS,qBAAAC,EAAmB,oBAAAC,EAAkB,qBAAAC,MAAyB,mBACvE,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,2BAAAC,MAA+B,sCACxC,OAAS,kBAAAC,MAAsB,2BAC/B,OAAS,cAAAC,MAAkB,mBAY3B,MAAMC,EAAYT,EAAM,WAA6C,CAACU,EAAOC,IAAiB,CAC5F,KAAM,CAAE,OAAQC,EAAe,GAAGC,CAAa,EAAIX,EAC7C,CAAE,UAAAY,EAAW,SAAAC,EAAU,OAAAC,EAAQ,GAAGC,CAAU,EAAIZ,EACpDK,EACAG,EACAN,CACF,EACMW,EAAwBZ,EAAwB,CACpD,MAAOU,EACP,UAAWd,EAAkB,OAAO,UACpC,WAAYA,EAAkB,OAAO,MACvC,CAAC,EACD,OACEF,EAAA,cAAC,OAAI,IAAKW,EAAc,UAAWV,EAAW,eAAgBa,CAAS,EAAI,GAAGG,GAC5EjB,EAAA,cAACQ,EAAA,KACCR,EAAA,cAAC,SAAM,UAAWC,EAAW,oBAAqBiB,CAAqB,GAAIH,CAAS,CACtF,CACF,CAEJ,CAAC,EACDN,EAAU,YAAc,aAIxB,MAAMU,EAAcnB,EAAM,WACxB,CAAC,CAAE,UAAAc,EAAW,GAAGJ,CAAM,EAAGC,IACxBX,EAAA,cAAC,SAAO,GAAGU,EAAO,IAAKC,EAAc,UAAWV,EAAW,iBAAkBa,CAAS,EAAG,CAE7F,EACAK,EAAY,YAAc,eAI1B,MAAMC,EAAYpB,EAAM,WACtB,CAAC,CAAE,UAAAc,EAAW,GAAGJ,CAAM,EAAGC,IACxBX,EAAA,cAAC,SAAO,GAAGU,EAAO,IAAKC,EAAc,UAAWV,EAAW,eAAgBa,CAAS,EAAG,CAE3F,EACAM,EAAU,YAAc,aAKxB,MAAMC,EAAWrB,EAAM,WAA2C,CAACU,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAG,EAAW,GAAGQ,CAAS,EAAIjB,EAAaK,EAAOP,CAAgB,EACvE,OAAOH,EAAA,cAAC,MAAI,GAAGsB,EAAU,IAAKX,EAAc,UAAWV,EAAW,cAAea,CAAS,EAAG,CAC/F,CAAC,EACDO,EAAS,YAAc,YAOvB,MAAME,EAAYvB,EAAM,WAA6C,CAACU,EAAOC,IAAiB,CAC5F,KAAM,CAAE,UAAAG,EAAW,GAAGU,CAAU,EAAInB,EAAaK,EAAON,CAAiB,EACzE,OAAOJ,EAAA,cAAC,MAAG,UAAWC,EAAW,eAAgBa,CAAS,EAAG,IAAKH,EAAe,GAAGa,EAAW,CACjG,CAAC,EACDD,EAAU,YAAc,aAMxB,MAAME,EAAwBzB,EAAM,WAGlC,CAACU,EAAOC,IAAiB,CACzB,KAAM,CAAE,UAAAG,EAAW,GAAGU,CAAU,EAAInB,EAAaK,EAAON,CAAiB,EACzE,OACEJ,EAAA,cAAC,MACC,UAAWC,EAAW,eAAgB,2BAA4Ba,CAAS,EAC3E,MAAM,MACN,IAAKH,EACJ,GAAGa,EACN,CAEJ,CAAC,EACDC,EAAsB,YAAc,yBAMpC,MAAMC,EAAqB1B,EAAM,WAC/B,CAACU,EAAOC,IAAiB,CACvB,KAAM,CAAE,UAAAG,EAAW,GAAGU,CAAU,EAAInB,EAAaK,EAAON,CAAiB,EACzE,OACEJ,EAAA,cAAC,MACC,UAAWC,EAAW,eAAgB,wBAAyBa,CAAS,EACxE,MAAM,MACN,IAAKH,EACJ,GAAGa,EACN,CAEJ,CACF,EACAE,EAAmB,YAAc", "names": ["React", "classNames", "tableRootPropDefs", "tableRowPropDefs", "tableCellPropDefs", "extractProps", "getResponsiveClassNames", "marginPropDefs", "ScrollArea", "TableRoot", "props", "forwardedRef", "layoutPropDef", "rootPropDefs", "className", "children", "layout", "rootProps", "tableLayoutClassNames", "TableHeader", "TableBody", "TableRow", "rowProps", "TableCell", "cellProps", "TableColumnHeaderCell", "TableRowHeaderCell"]}