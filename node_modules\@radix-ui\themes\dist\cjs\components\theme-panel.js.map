{"version": 3, "sources": ["../../../src/components/theme-panel.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useCallbackRef } from 'radix-ui/internal';\n\nimport {\n  AccessibleIcon,\n  Box,\n  Button,\n  Flex,\n  Grid,\n  Heading,\n  IconButton,\n  Kbd,\n  Popover,\n  ScrollArea,\n  Text,\n  Tooltip,\n} from '../index.js';\nimport { Theme, useThemeContext } from './theme.js';\nimport { inert } from '../helpers/inert.js';\nimport { getMatchingGrayColor } from '../helpers/get-matching-gray-color.js';\nimport { themePropDefs } from './theme.props.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ninterface ThemePanelProps extends Omit<ThemePanelImplProps, keyof ThemePanelImplPrivateProps> {\n  defaultOpen?: boolean;\n}\nconst ThemePanel = React.forwardRef<ThemePanelImplElement, ThemePanelProps>(\n  ({ defaultOpen = true, ...props }, forwardedRef) => {\n    const [open, setOpen] = React.useState(defaultOpen);\n    return <ThemePanelImpl {...props} ref={forwardedRef} open={open} onOpenChange={setOpen} />;\n  }\n);\nThemePanel.displayName = 'ThemePanel';\n\ntype ThemePanelImplElement = React.ElementRef<'div'>;\ninterface ThemePanelImplPrivateProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\ninterface ThemePanelImplProps\n  extends ComponentPropsWithout<'div', RemovedProps>,\n    ThemePanelImplPrivateProps {\n  onAppearanceChange?: (value: 'light' | 'dark') => void;\n}\nconst ThemePanelImpl = React.forwardRef<ThemePanelImplElement, ThemePanelImplProps>(\n  (props, forwardedRef) => {\n    const { open, onOpenChange, onAppearanceChange: onAppearanceChangeProp, ...panelProps } = props;\n    const themeContext = useThemeContext();\n    const {\n      appearance,\n      onAppearanceChange,\n      accentColor,\n      onAccentColorChange,\n      grayColor,\n      onGrayColorChange,\n      panelBackground,\n      onPanelBackgroundChange,\n      radius,\n      onRadiusChange,\n      scaling,\n      onScalingChange,\n    } = themeContext;\n\n    const hasOnAppearanceChangeProp = onAppearanceChangeProp !== undefined;\n    const handleAppearanceChangeProp = useCallbackRef(onAppearanceChangeProp);\n    const handleAppearanceChange = React.useCallback(\n      (value: 'light' | 'dark') => {\n        const cleanup = disableAnimation();\n\n        if (appearance !== 'inherit') {\n          onAppearanceChange(value);\n          return;\n        }\n\n        if (hasOnAppearanceChangeProp) {\n          handleAppearanceChangeProp(value);\n        } else {\n          setResolvedAppearance(value);\n          updateRootAppearanceClass(value);\n        }\n\n        cleanup();\n      },\n      [appearance, onAppearanceChange, hasOnAppearanceChangeProp, handleAppearanceChangeProp]\n    );\n\n    const autoMatchedGray = getMatchingGrayColor(accentColor);\n    const resolvedGrayColor = grayColor === 'auto' ? autoMatchedGray : grayColor;\n\n    const [copyState, setCopyState] = React.useState<'idle' | 'copying' | 'copied'>('idle');\n    async function handleCopyThemeConfig() {\n      const theme = {\n        appearance: appearance === themePropDefs.appearance.default ? undefined : appearance,\n        accentColor: accentColor === themePropDefs.accentColor.default ? undefined : accentColor,\n        grayColor: grayColor === themePropDefs.grayColor.default ? undefined : grayColor,\n        panelBackground:\n          panelBackground === themePropDefs.panelBackground.default ? undefined : panelBackground,\n        radius: radius === themePropDefs.radius.default ? undefined : radius,\n        scaling: scaling === themePropDefs.scaling.default ? undefined : scaling,\n      } satisfies GetPropDefTypes<typeof themePropDefs>;\n\n      const props = Object.keys(theme)\n        .filter((key) => theme[key as keyof typeof theme] !== undefined)\n        .map((key) => `${key}=\"${theme[key as keyof typeof theme]}\"`)\n        .join(' ');\n\n      const textToCopy = props ? `<Theme ${props}>` : '<Theme>';\n\n      setCopyState('copying');\n      await navigator.clipboard.writeText(textToCopy);\n      setCopyState('copied');\n      setTimeout(() => setCopyState('idle'), 2000);\n    }\n\n    const [resolvedAppearance, setResolvedAppearance] = React.useState<'light' | 'dark' | null>(\n      appearance === 'inherit' ? null : appearance\n    );\n\n    const keyboardInputElement = `\n      [contenteditable],\n      [role=\"combobox\"],\n      [role=\"listbox\"],\n      [role=\"menu\"],\n      input:not([type=\"radio\"], [type=\"checkbox\"]),\n      select,\n      textarea\n    `;\n\n    // quickly show/hide using \"T\" keypress\n    React.useEffect(() => {\n      function handleKeydown(event: KeyboardEvent) {\n        const isModifierActive = event.altKey || event.ctrlKey || event.shiftKey || event.metaKey;\n        const isKeyboardInputActive = document.activeElement?.closest(keyboardInputElement);\n        const isKeyT = event.key?.toUpperCase() === 'T' && !isModifierActive;\n        if (isKeyT && !isKeyboardInputActive) {\n          onOpenChange(!open);\n        }\n      }\n      document.addEventListener('keydown', handleKeydown);\n      return () => document.removeEventListener('keydown', handleKeydown);\n    }, [onOpenChange, open, keyboardInputElement]);\n\n    // quickly toggle appearance using \"D\" keypress\n    React.useEffect(() => {\n      function handleKeydown(event: KeyboardEvent) {\n        const isModifierActive = event.altKey || event.ctrlKey || event.shiftKey || event.metaKey;\n        const isKeyboardInputActive = document.activeElement?.closest(keyboardInputElement);\n        const isKeyD = event.key?.toUpperCase() === 'D' && !isModifierActive;\n        if (isKeyD && !isKeyboardInputActive) {\n          handleAppearanceChange(resolvedAppearance === 'light' ? 'dark' : 'light');\n        }\n      }\n      document.addEventListener('keydown', handleKeydown);\n      return () => document.removeEventListener('keydown', handleKeydown);\n    }, [handleAppearanceChange, resolvedAppearance, keyboardInputElement]);\n\n    React.useEffect(() => {\n      const root = document.documentElement;\n      const body = document.body;\n\n      function update() {\n        const hasDarkClass =\n          root.classList.contains('dark') ||\n          root.classList.contains('dark-theme') ||\n          body.classList.contains('dark') ||\n          body.classList.contains('dark-theme');\n\n        if (appearance === 'inherit') {\n          setResolvedAppearance(hasDarkClass ? 'dark' : 'light');\n        } else {\n          setResolvedAppearance(appearance);\n        }\n      }\n\n      const classNameObserver = new MutationObserver(function (mutations) {\n        mutations.forEach(function (mutation) {\n          if (mutation.attributeName === 'class') {\n            update();\n          }\n        });\n      });\n\n      update();\n\n      // Observe <html> and <body> for `class` changes only when the appearance is inherited from them\n      if (appearance === 'inherit') {\n        classNameObserver.observe(root, { attributes: true });\n        classNameObserver.observe(body, { attributes: true });\n      }\n\n      return () => classNameObserver.disconnect();\n    }, [appearance]);\n\n    return (\n      <Theme asChild radius=\"medium\" scaling=\"100%\">\n        <Flex\n          direction=\"column\"\n          position=\"fixed\"\n          top=\"0\"\n          right=\"0\"\n          mr=\"4\"\n          mt=\"4\"\n          // @ts-ignore\n          inert={open ? undefined : inert}\n          {...panelProps}\n          ref={forwardedRef}\n          style={{\n            zIndex: 9999,\n            overflow: 'hidden',\n            maxHeight: 'calc(100vh - var(--space-4) - var(--space-4))',\n            borderRadius: 'var(--radius-4)',\n            backgroundColor: 'var(--color-panel-solid)',\n            transformOrigin: 'top center',\n            transitionProperty: 'transform, box-shadow',\n            transitionDuration: '200ms',\n            transitionTimingFunction: open ? 'ease-out' : 'ease-in',\n            transform: open ? 'none' : 'translateX(105%)',\n            boxShadow: open ? 'var(--shadow-5)' : 'var(--shadow-2)',\n            ...props.style,\n          }}\n        >\n          <ScrollArea>\n            <Box flexGrow=\"1\" p=\"5\" position=\"relative\">\n              <Box position=\"absolute\" top=\"0\" right=\"0\" m=\"2\">\n                <Tooltip\n                  content=\"Press T to show/hide the Theme Panel\"\n                  side=\"bottom\"\n                  sideOffset={6}\n                >\n                  <Kbd asChild size=\"3\" tabIndex={0} className=\"rt-ThemePanelShortcut\">\n                    <button onClick={() => onOpenChange(!open)}>T</button>\n                  </Kbd>\n                </Tooltip>\n              </Box>\n\n              <Heading size=\"5\" trim=\"both\" as=\"h3\" mb=\"5\">\n                Theme\n              </Heading>\n\n              <Text id=\"accent-color-title\" as=\"p\" size=\"2\" weight=\"medium\" mt=\"5\">\n                Accent color\n              </Text>\n\n              <Grid columns=\"10\" gap=\"2\" mt=\"3\" role=\"group\" aria-labelledby=\"accent-color-title\">\n                {themePropDefs.accentColor.values.map((color) => (\n                  <label\n                    key={color}\n                    className=\"rt-ThemePanelSwatch\"\n                    style={{ backgroundColor: `var(--${color}-9)` }}\n                  >\n                    <Tooltip\n                      content={`${upperFirst(color)}${\n                        accentColor === 'gray' && resolvedGrayColor !== 'gray'\n                          ? ` (${upperFirst(resolvedGrayColor)})`\n                          : ''\n                      }`}\n                    >\n                      <input\n                        className=\"rt-ThemePanelSwatchInput\"\n                        type=\"radio\"\n                        name=\"accentColor\"\n                        value={color}\n                        checked={accentColor === color}\n                        onChange={(event) =>\n                          onAccentColorChange(event.target.value as typeof accentColor)\n                        }\n                      />\n                    </Tooltip>\n                  </label>\n                ))}\n              </Grid>\n\n              <Flex asChild align=\"center\" justify=\"between\">\n                <Text as=\"p\" id=\"gray-color-title\" size=\"2\" weight=\"medium\" mt=\"5\">\n                  Gray color\n                </Text>\n              </Flex>\n\n              <Grid columns=\"10\" gap=\"2\" mt=\"3\" role=\"group\" aria-labelledby=\"gray-color-title\">\n                {themePropDefs.grayColor.values.map((gray) => (\n                  <Flex key={gray} asChild align=\"center\" justify=\"center\">\n                    <label\n                      className=\"rt-ThemePanelSwatch\"\n                      style={{\n                        backgroundColor:\n                          gray === 'auto'\n                            ? `var(--${autoMatchedGray}-9)`\n                            : gray === 'gray'\n                            ? 'var(--gray-9)'\n                            : `var(--${gray}-9)`,\n                        // we override --gray so pure gray doesn't exist anymore\n                        // recover something as close as possible by desaturating\n                        filter: gray === 'gray' ? 'saturate(0)' : undefined,\n                      }}\n                    >\n                      <Tooltip\n                        content={`${upperFirst(gray)}${\n                          gray === 'auto' ? ` (${upperFirst(autoMatchedGray)})` : ''\n                        }`}\n                      >\n                        <input\n                          className=\"rt-ThemePanelSwatchInput\"\n                          type=\"radio\"\n                          name=\"grayColor\"\n                          value={gray}\n                          checked={grayColor === gray}\n                          onChange={(event) =>\n                            onGrayColorChange(event.target.value as typeof grayColor)\n                          }\n                        />\n                      </Tooltip>\n                    </label>\n                  </Flex>\n                ))}\n              </Grid>\n\n              <Text id=\"appearance-title\" as=\"p\" size=\"2\" weight=\"medium\" mt=\"5\">\n                Appearance\n              </Text>\n\n              <Grid columns=\"2\" gap=\"2\" mt=\"3\" role=\"group\" aria-labelledby=\"appearance-title\">\n                {(['light', 'dark'] as const).map((value) => (\n                  <label key={value} className=\"rt-ThemePanelRadioCard\">\n                    <input\n                      className=\"rt-ThemePanelRadioCardInput\"\n                      type=\"radio\"\n                      name=\"appearance\"\n                      value={value}\n                      checked={resolvedAppearance === value}\n                      onChange={(event) =>\n                        handleAppearanceChange(event.target.value as 'light' | 'dark')\n                      }\n                    />\n                    <Flex align=\"center\" justify=\"center\" height=\"32px\" gap=\"2\">\n                      {value === 'light' ? (\n                        <svg\n                          width=\"15\"\n                          height=\"15\"\n                          viewBox=\"0 0 15 15\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          style={{ margin: '0 -1px' }}\n                        >\n                          <path\n                            d=\"M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                        </svg>\n                      ) : (\n                        <svg\n                          width=\"15\"\n                          height=\"15\"\n                          viewBox=\"0 0 15 15\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          style={{ margin: '0 -1px' }}\n                        >\n                          <path\n                            d=\"M2.89998 0.499976C2.89998 0.279062 2.72089 0.0999756 2.49998 0.0999756C2.27906 0.0999756 2.09998 0.279062 2.09998 0.499976V1.09998H1.49998C1.27906 1.09998 1.09998 1.27906 1.09998 1.49998C1.09998 1.72089 1.27906 1.89998 1.49998 1.89998H2.09998V2.49998C2.09998 2.72089 2.27906 2.89998 2.49998 2.89998C2.72089 2.89998 2.89998 2.72089 2.89998 2.49998V1.89998H3.49998C3.72089 1.89998 3.89998 1.72089 3.89998 1.49998C3.89998 1.27906 3.72089 1.09998 3.49998 1.09998H2.89998V0.499976ZM5.89998 3.49998C5.89998 3.27906 5.72089 3.09998 5.49998 3.09998C5.27906 3.09998 5.09998 3.27906 5.09998 3.49998V4.09998H4.49998C4.27906 4.09998 4.09998 4.27906 4.09998 4.49998C4.09998 4.72089 4.27906 4.89998 4.49998 4.89998H5.09998V5.49998C5.09998 5.72089 5.27906 5.89998 5.49998 5.89998C5.72089 5.89998 5.89998 5.72089 5.89998 5.49998V4.89998H6.49998C6.72089 4.89998 6.89998 4.72089 6.89998 4.49998C6.89998 4.27906 6.72089 4.09998 6.49998 4.09998H5.89998V3.49998ZM1.89998 6.49998C1.89998 6.27906 1.72089 6.09998 1.49998 6.09998C1.27906 6.09998 1.09998 6.27906 1.09998 6.49998V7.09998H0.499976C0.279062 7.09998 0.0999756 7.27906 0.0999756 7.49998C0.0999756 7.72089 0.279062 7.89998 0.499976 7.89998H1.09998V8.49998C1.09998 8.72089 1.27906 8.89997 1.49998 8.89997C1.72089 8.89997 1.89998 8.72089 1.89998 8.49998V7.89998H2.49998C2.72089 7.89998 2.89998 7.72089 2.89998 7.49998C2.89998 7.27906 2.72089 7.09998 2.49998 7.09998H1.89998V6.49998ZM8.54406 0.98184L8.24618 0.941586C8.03275 0.917676 7.90692 1.1655 8.02936 1.34194C8.17013 1.54479 8.29981 1.75592 8.41754 1.97445C8.91878 2.90485 9.20322 3.96932 9.20322 5.10022C9.20322 8.37201 6.82247 11.0878 3.69887 11.6097C3.45736 11.65 3.20988 11.6772 2.96008 11.6906C2.74563 11.702 2.62729 11.9535 2.77721 12.1072C2.84551 12.1773 2.91535 12.2458 2.98667 12.3128L3.05883 12.3795L3.31883 12.6045L3.50684 12.7532L3.62796 12.8433L3.81491 12.9742L3.99079 13.089C4.11175 13.1651 4.23536 13.2375 4.36157 13.3059L4.62496 13.4412L4.88553 13.5607L5.18837 13.6828L5.43169 13.7686C5.56564 13.8128 5.70149 13.8529 5.83857 13.8885C5.94262 13.9155 6.04767 13.9401 6.15405 13.9622C6.27993 13.9883 6.40713 14.0109 6.53544 14.0298L6.85241 14.0685L7.11934 14.0892C7.24637 14.0965 7.37436 14.1002 7.50322 14.1002C11.1483 14.1002 14.1032 11.1453 14.1032 7.50023C14.1032 7.25044 14.0893 7.00389 14.0623 6.76131L14.0255 6.48407C13.991 6.26083 13.9453 6.04129 13.8891 5.82642C13.8213 5.56709 13.7382 5.31398 13.6409 5.06881L13.5279 4.80132L13.4507 4.63542L13.3766 4.48666C13.2178 4.17773 13.0353 3.88295 12.8312 3.60423L12.6782 3.40352L12.4793 3.16432L12.3157 2.98361L12.1961 2.85951L12.0355 2.70246L11.8134 2.50184L11.4925 2.24191L11.2483 2.06498L10.9562 1.87446L10.6346 1.68894L10.3073 1.52378L10.1938 1.47176L9.95488 1.3706L9.67791 1.2669L9.42566 1.1846L9.10075 1.09489L8.83599 1.03486L8.54406 0.98184ZM10.4032 5.30023C10.4032 4.27588 10.2002 3.29829 9.83244 2.40604C11.7623 3.28995 13.1032 5.23862 13.1032 7.50023C13.1032 10.593 10.596 13.1002 7.50322 13.1002C6.63646 13.1002 5.81597 12.9036 5.08355 12.5522C6.5419 12.0941 7.81081 11.2082 8.74322 10.0416C8.87963 10.2284 9.10028 10.3497 9.34928 10.3497C9.76349 10.3497 10.0993 10.0139 10.0993 9.59971C10.0993 9.24256 9.84965 8.94373 9.51535 8.86816C9.57741 8.75165 9.63653 8.63334 9.6926 8.51332C9.88358 8.63163 10.1088 8.69993 10.35 8.69993C11.0403 8.69993 11.6 8.14028 11.6 7.44993C11.6 6.75976 11.0406 6.20024 10.3505 6.19993C10.3853 5.90487 10.4032 5.60464 10.4032 5.30023Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                        </svg>\n                      )}\n                      <Text size=\"1\" weight=\"medium\">\n                        {upperFirst(value)}\n                      </Text>\n                    </Flex>\n                  </label>\n                ))}\n              </Grid>\n\n              <Text id=\"radius-title\" as=\"p\" size=\"2\" weight=\"medium\" mt=\"5\">\n                Radius\n              </Text>\n\n              <Grid columns=\"5\" gap=\"2\" mt=\"3\" role=\"group\" aria-labelledby=\"radius-title\">\n                {themePropDefs.radius.values.map((value) => (\n                  <Flex key={value} direction=\"column\" align=\"center\">\n                    <label className=\"rt-ThemePanelRadioCard\">\n                      <input\n                        className=\"rt-ThemePanelRadioCardInput\"\n                        type=\"radio\"\n                        name=\"radius\"\n                        id={`theme-panel-radius-${value}`}\n                        value={value}\n                        checked={radius === value}\n                        onChange={(event) => onRadiusChange(event.target.value as typeof radius)}\n                      />\n                      <Theme asChild radius={value}>\n                        <Box\n                          m=\"3\"\n                          width=\"32px\"\n                          height=\"32px\"\n                          style={{\n                            borderTopLeftRadius: value === 'full' ? '80%' : 'var(--radius-5)',\n                            backgroundImage:\n                              'linear-gradient(to bottom right, var(--accent-3), var(--accent-4))',\n                            borderTop: '2px solid var(--accent-a8)',\n                            borderLeft: '2px solid var(--accent-a8)',\n                          }}\n                        />\n                      </Theme>\n                    </label>\n                    <Box asChild pt=\"2\">\n                      <Text asChild size=\"1\" color=\"gray\">\n                        <label htmlFor={`theme-panel-radius-${value}`}>{upperFirst(value)}</label>\n                      </Text>\n                    </Box>\n                  </Flex>\n                ))}\n              </Grid>\n\n              <Text id=\"scaling-title\" as=\"p\" size=\"2\" weight=\"medium\" mt=\"5\">\n                Scaling\n              </Text>\n\n              <Grid columns=\"5\" gap=\"2\" mt=\"3\" role=\"group\" aria-labelledby=\"scaling-title\">\n                {themePropDefs.scaling.values.map((value) => (\n                  <label key={value} className=\"rt-ThemePanelRadioCard\">\n                    <input\n                      className=\"rt-ThemePanelRadioCardInput\"\n                      type=\"radio\"\n                      name=\"scaling\"\n                      value={value}\n                      checked={scaling === value}\n                      onChange={(event) => onScalingChange(event.target.value as typeof scaling)}\n                    />\n\n                    <Flex align=\"center\" justify=\"center\" height=\"32px\">\n                      <Theme asChild scaling={value}>\n                        <Flex align=\"center\" justify=\"center\">\n                          <Text size=\"1\" weight=\"medium\">\n                            {upperFirst(value)}\n                          </Text>\n                        </Flex>\n                      </Theme>\n                    </Flex>\n                  </label>\n                ))}\n              </Grid>\n\n              <Flex mt=\"5\" align=\"center\" gap=\"2\">\n                <Text id=\"panel-background-title\" as=\"p\" size=\"2\" weight=\"medium\">\n                  Panel background\n                </Text>\n\n                <Popover.Root>\n                  <Popover.Trigger>\n                    <IconButton size=\"1\" variant=\"ghost\" color=\"gray\">\n                      <AccessibleIcon label=\"Learn more about panel background options\">\n                        <svg\n                          width=\"15\"\n                          height=\"15\"\n                          viewBox=\"0 0 15 15\"\n                          fill=\"currentColor\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M7.49991 0.876892C3.84222 0.876892 0.877075 3.84204 0.877075 7.49972C0.877075 11.1574 3.84222 14.1226 7.49991 14.1226C11.1576 14.1226 14.1227 11.1574 14.1227 7.49972C14.1227 3.84204 11.1576 0.876892 7.49991 0.876892ZM1.82707 7.49972C1.82707 4.36671 4.36689 1.82689 7.49991 1.82689C10.6329 1.82689 13.1727 4.36671 13.1727 7.49972C13.1727 10.6327 10.6329 13.1726 7.49991 13.1726C4.36689 13.1726 1.82707 10.6327 1.82707 7.49972ZM8.24992 4.49999C8.24992 4.9142 7.91413 5.24999 7.49992 5.24999C7.08571 5.24999 6.74992 4.9142 6.74992 4.49999C6.74992 4.08577 7.08571 3.74999 7.49992 3.74999C7.91413 3.74999 8.24992 4.08577 8.24992 4.49999ZM6.00003 5.99999H6.50003H7.50003C7.77618 5.99999 8.00003 6.22384 8.00003 6.49999V9.99999H8.50003H9.00003V11H8.50003H7.50003H6.50003H6.00003V9.99999H6.50003H7.00003V6.99999H6.50003H6.00003V5.99999Z\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          />\n                        </svg>\n                      </AccessibleIcon>\n                    </IconButton>\n                  </Popover.Trigger>\n\n                  <Popover.Content size=\"1\" style={{ maxWidth: 220 }} side=\"top\" align=\"center\">\n                    <Text as=\"p\" size=\"2\">\n                      Whether Card and Table panels are translucent, showing some of the background\n                      behind them.\n                    </Text>\n                  </Popover.Content>\n                </Popover.Root>\n              </Flex>\n\n              <Grid\n                columns=\"2\"\n                gap=\"2\"\n                mt=\"3\"\n                role=\"group\"\n                aria-labelledby=\"panel-background-title\"\n              >\n                {themePropDefs.panelBackground.values.map((value) => (\n                  <label key={value} className=\"rt-ThemePanelRadioCard\">\n                    <input\n                      className=\"rt-ThemePanelRadioCardInput\"\n                      type=\"radio\"\n                      name=\"panelBackground\"\n                      value={value}\n                      checked={panelBackground === value}\n                      onChange={(event) =>\n                        onPanelBackgroundChange(event.target.value as typeof panelBackground)\n                      }\n                    />\n                    <Flex align=\"center\" justify=\"center\" height=\"32px\" gap=\"2\">\n                      {value === 'solid' ? (\n                        <svg\n                          width=\"15\"\n                          height=\"15\"\n                          viewBox=\"0 0 15 15\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          style={{ margin: '0 -2px' }}\n                        >\n                          <path\n                            d=\"M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                        </svg>\n                      ) : (\n                        <svg\n                          width=\"15\"\n                          height=\"15\"\n                          viewBox=\"0 0 15 15\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          style={{ margin: '0 -2px' }}\n                        >\n                          <path\n                            opacity=\".05\"\n                            d=\"M6.78296 13.376C8.73904 9.95284 8.73904 5.04719 6.78296 1.62405L7.21708 1.37598C9.261 4.95283 9.261 10.0472 7.21708 13.624L6.78296 13.376Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".1\"\n                            d=\"M7.28204 13.4775C9.23929 9.99523 9.23929 5.00475 7.28204 1.52248L7.71791 1.2775C9.76067 4.9119 9.76067 10.0881 7.71791 13.7225L7.28204 13.4775Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".15\"\n                            d=\"M7.82098 13.5064C9.72502 9.99523 9.72636 5.01411 7.82492 1.50084L8.26465 1.26285C10.2465 4.92466 10.2451 10.085 8.26052 13.7448L7.82098 13.5064Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".2\"\n                            d=\"M8.41284 13.429C10.1952 9.92842 10.1957 5.07537 8.41435 1.57402L8.85999 1.34729C10.7139 4.99113 10.7133 10.0128 8.85841 13.6559L8.41284 13.429Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".25\"\n                            d=\"M9.02441 13.2956C10.6567 9.8379 10.6586 5.17715 9.03005 1.71656L9.48245 1.50366C11.1745 5.09919 11.1726 9.91629 9.47657 13.5091L9.02441 13.2956Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".3\"\n                            d=\"M9.66809 13.0655C11.1097 9.69572 11.1107 5.3121 9.67088 1.94095L10.1307 1.74457C11.6241 5.24121 11.6231 9.76683 10.1278 13.2622L9.66809 13.0655Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".35\"\n                            d=\"M10.331 12.7456C11.5551 9.52073 11.5564 5.49103 10.3347 2.26444L10.8024 2.0874C12.0672 5.42815 12.0659 9.58394 10.7985 12.9231L10.331 12.7456Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".4\"\n                            d=\"M11.0155 12.2986C11.9938 9.29744 11.9948 5.71296 11.0184 2.71067L11.4939 2.55603C12.503 5.6589 12.502 9.35178 11.4909 12.4535L11.0155 12.2986Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".45\"\n                            d=\"M11.7214 11.668C12.4254 9.01303 12.4262 5.99691 11.7237 3.34116L12.2071 3.21329C12.9318 5.95292 12.931 9.05728 12.2047 11.7961L11.7214 11.668Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            opacity=\".5\"\n                            d=\"M12.4432 10.752C12.8524 8.63762 12.8523 6.36089 12.4429 4.2466L12.9338 4.15155C13.3553 6.32861 13.3554 8.66985 12.9341 10.847L12.4432 10.752Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                          <path\n                            d=\"M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704Z\"\n                            fill=\"currentColor\"\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                          ></path>\n                        </svg>\n                      )}\n                      <Text size=\"1\" weight=\"medium\">\n                        {upperFirst(value)}\n                      </Text>\n                    </Flex>\n                  </label>\n                ))}\n              </Grid>\n\n              <Button mt=\"5\" style={{ width: '100%' }} onClick={handleCopyThemeConfig}>\n                {copyState === 'copied' ? 'Copied' : 'Copy Theme'}\n              </Button>\n            </Box>\n          </ScrollArea>\n        </Flex>\n      </Theme>\n    );\n  }\n);\nThemePanelImpl.displayName = 'ThemePanelImpl';\n\n// https://github.com/pacocoursey/next-themes/blob/main/packages/next-themes/src/index.tsx#L285\nfunction disableAnimation() {\n  const css = document.createElement('style');\n  css.appendChild(\n    document.createTextNode(\n      `*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}`\n    )\n  );\n  document.head.appendChild(css);\n\n  return () => {\n    // Force restyle\n    (() => window.getComputedStyle(document.body))();\n\n    // Wait for next tick before removing\n    setTimeout(() => {\n      document.head.removeChild(css);\n    }, 1);\n  };\n}\n\nfunction upperFirst(string: string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\nfunction updateRootAppearanceClass(appearance: 'light' | 'dark') {\n  const root = document.documentElement;\n  const hasLightTheme = root.classList.contains('light-theme');\n  const hasDarkTheme = root.classList.contains('dark-theme');\n  const hasLight = root.classList.contains('light');\n  const hasDark = root.classList.contains('dark');\n\n  if (hasLightTheme || hasDarkTheme) {\n    root.classList.remove('light-theme', 'dark-theme');\n    root.style.colorScheme = appearance;\n    root.classList.add(`${appearance}-theme`);\n  }\n\n  if (hasLight || hasDark) {\n    root.classList.remove('light', 'dark');\n    root.style.colorScheme = appearance;\n    root.classList.add(appearance);\n  }\n\n  if (!hasLightTheme && !hasDarkTheme && !hasLight && !hasDark) {\n    root.style.colorScheme = appearance;\n    root.classList.add(appearance);\n  }\n}\n\nexport { ThemePanel };\nexport type { ThemePanelProps };\n"], "mappings": "ykBAAA,IAAAA,GAAA,GAAAC,EAAAD,GAAA,gBAAAE,IAAA,eAAAC,GAAAH,IAEA,IAAAI,EAAuB,qBACvBC,EAA+B,6BAE/BC,EAaO,uBACPC,EAAuC,sBACvCC,EAAsB,+BACtBC,EAAqC,iDACrCC,EAA8B,4BAQ9B,MAAMR,EAAaE,EAAM,WACvB,CAAC,CAAE,YAAAO,EAAc,GAAM,GAAGC,CAAM,EAAGC,IAAiB,CAClD,KAAM,CAACC,EAAMC,CAAO,EAAIX,EAAM,SAASO,CAAW,EAClD,OAAOP,EAAA,cAACY,EAAA,CAAgB,GAAGJ,EAAO,IAAKC,EAAc,KAAMC,EAAM,aAAcC,EAAS,CAC1F,CACF,EACAb,EAAW,YAAc,aAYzB,MAAMc,EAAiBZ,EAAM,WAC3B,CAACQ,EAAOC,IAAiB,CACvB,KAAM,CAAE,KAAAC,EAAM,aAAAG,EAAc,mBAAoBC,EAAwB,GAAGC,CAAW,EAAIP,EACpFQ,KAAe,mBAAgB,EAC/B,CACJ,WAAAC,EACA,mBAAAC,EACA,YAAAC,EACA,oBAAAC,EACA,UAAAC,EACA,kBAAAC,EACA,gBAAAC,EACA,wBAAAC,EACA,OAAAC,EACA,eAAAC,EACA,QAAAC,EACA,gBAAAC,CACF,EAAIZ,EAEEa,EAA4Bf,IAA2B,OACvDgB,KAA6B,kBAAehB,CAAsB,EAClEiB,EAAyB/B,EAAM,YAClCgC,GAA4B,CAC3B,MAAMC,EAAUC,GAAiB,EAEjC,GAAIjB,IAAe,UAAW,CAC5BC,EAAmBc,CAAK,EACxB,MACF,CAEIH,EACFC,EAA2BE,CAAK,GAEhCG,EAAsBH,CAAK,EAC3BI,GAA0BJ,CAAK,GAGjCC,EAAQ,CACV,EACA,CAAChB,EAAYC,EAAoBW,EAA2BC,CAA0B,CACxF,EAEMO,KAAkB,wBAAqBlB,CAAW,EAClDmB,EAAoBjB,IAAc,OAASgB,EAAkBhB,EAE7D,CAACkB,EAAWC,CAAY,EAAIxC,EAAM,SAAwC,MAAM,EACtF,eAAeyC,GAAwB,CACrC,MAAMC,EAAQ,CACZ,WAAYzB,IAAe,gBAAc,WAAW,QAAU,OAAYA,EAC1E,YAAaE,IAAgB,gBAAc,YAAY,QAAU,OAAYA,EAC7E,UAAWE,IAAc,gBAAc,UAAU,QAAU,OAAYA,EACvE,gBACEE,IAAoB,gBAAc,gBAAgB,QAAU,OAAYA,EAC1E,OAAQE,IAAW,gBAAc,OAAO,QAAU,OAAYA,EAC9D,QAASE,IAAY,gBAAc,QAAQ,QAAU,OAAYA,CACnE,EAEMnB,EAAQ,OAAO,KAAKkC,CAAK,EAC5B,OAAQC,GAAQD,EAAMC,CAAyB,IAAM,MAAS,EAC9D,IAAKA,GAAQ,GAAGA,CAAG,KAAKD,EAAMC,CAAyB,CAAC,GAAG,EAC3D,KAAK,GAAG,EAELC,EAAapC,EAAQ,UAAUA,CAAK,IAAM,UAEhDgC,EAAa,SAAS,EACtB,MAAM,UAAU,UAAU,UAAUI,CAAU,EAC9CJ,EAAa,QAAQ,EACrB,WAAW,IAAMA,EAAa,MAAM,EAAG,GAAI,CAC7C,CAEA,KAAM,CAACK,EAAoBV,CAAqB,EAAInC,EAAM,SACxDiB,IAAe,UAAY,KAAOA,CACpC,EAEM6B,EAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAW7B,OAAA9C,EAAM,UAAU,IAAM,CACpB,SAAS+C,EAAcC,EAAsB,CAC3C,MAAMC,EAAmBD,EAAM,QAAUA,EAAM,SAAWA,EAAM,UAAYA,EAAM,QAC5EE,EAAwB,SAAS,eAAe,QAAQJ,CAAoB,EACnEE,EAAM,KAAK,YAAY,IAAM,KAAO,CAACC,GACtC,CAACC,GACbrC,EAAa,CAACH,CAAI,CAEtB,CACA,gBAAS,iBAAiB,UAAWqC,CAAa,EAC3C,IAAM,SAAS,oBAAoB,UAAWA,CAAa,CACpE,EAAG,CAAClC,EAAcH,EAAMoC,CAAoB,CAAC,EAG7C9C,EAAM,UAAU,IAAM,CACpB,SAAS+C,EAAcC,EAAsB,CAC3C,MAAMC,EAAmBD,EAAM,QAAUA,EAAM,SAAWA,EAAM,UAAYA,EAAM,QAC5EE,EAAwB,SAAS,eAAe,QAAQJ,CAAoB,EACnEE,EAAM,KAAK,YAAY,IAAM,KAAO,CAACC,GACtC,CAACC,GACbnB,EAAuBc,IAAuB,QAAU,OAAS,OAAO,CAE5E,CACA,gBAAS,iBAAiB,UAAWE,CAAa,EAC3C,IAAM,SAAS,oBAAoB,UAAWA,CAAa,CACpE,EAAG,CAAChB,EAAwBc,EAAoBC,CAAoB,CAAC,EAErE9C,EAAM,UAAU,IAAM,CACpB,MAAMmD,EAAO,SAAS,gBAChBC,EAAO,SAAS,KAEtB,SAASC,GAAS,CAChB,MAAMC,EACJH,EAAK,UAAU,SAAS,MAAM,GAC9BA,EAAK,UAAU,SAAS,YAAY,GACpCC,EAAK,UAAU,SAAS,MAAM,GAC9BA,EAAK,UAAU,SAAS,YAAY,EAGpCjB,EADElB,IAAe,UACKqC,EAAe,OAAS,QAExBrC,CAF+B,CAIzD,CAEA,MAAMsC,EAAoB,IAAI,iBAAiB,SAAUC,EAAW,CAClEA,EAAU,QAAQ,SAAUC,EAAU,CAChCA,EAAS,gBAAkB,SAC7BJ,EAAO,CAEX,CAAC,CACH,CAAC,EAED,OAAAA,EAAO,EAGHpC,IAAe,YACjBsC,EAAkB,QAAQJ,EAAM,CAAE,WAAY,EAAK,CAAC,EACpDI,EAAkB,QAAQH,EAAM,CAAE,WAAY,EAAK,CAAC,GAG/C,IAAMG,EAAkB,WAAW,CAC5C,EAAG,CAACtC,CAAU,CAAC,EAGbjB,EAAA,cAAC,SAAM,QAAO,GAAC,OAAO,SAAS,QAAQ,QACrCA,EAAA,cAAC,QACC,UAAU,SACV,SAAS,QACT,IAAI,IACJ,MAAM,IACN,GAAG,IACH,GAAG,IAEH,MAAOU,EAAO,OAAY,QACzB,GAAGK,EACJ,IAAKN,EACL,MAAO,CACL,OAAQ,KACR,SAAU,SACV,UAAW,gDACX,aAAc,kBACd,gBAAiB,2BACjB,gBAAiB,aACjB,mBAAoB,wBACpB,mBAAoB,QACpB,yBAA0BC,EAAO,WAAa,UAC9C,UAAWA,EAAO,OAAS,mBAC3B,UAAWA,EAAO,kBAAoB,kBACtC,GAAGF,EAAM,KACX,GAEAR,EAAA,cAAC,kBACCA,EAAA,cAAC,OAAI,SAAS,IAAI,EAAE,IAAI,SAAS,YAC/BA,EAAA,cAAC,OAAI,SAAS,WAAW,IAAI,IAAI,MAAM,IAAI,EAAE,KAC3CA,EAAA,cAAC,WACC,QAAQ,uCACR,KAAK,SACL,WAAY,GAEZA,EAAA,cAAC,OAAI,QAAO,GAAC,KAAK,IAAI,SAAU,EAAG,UAAU,yBAC3CA,EAAA,cAAC,UAAO,QAAS,IAAMa,EAAa,CAACH,CAAI,GAAG,GAAC,CAC/C,CACF,CACF,EAEAV,EAAA,cAAC,WAAQ,KAAK,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,KAAI,OAE7C,EAEAA,EAAA,cAAC,QAAK,GAAG,qBAAqB,GAAG,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG,KAAI,cAErE,EAEAA,EAAA,cAAC,QAAK,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,kBAAgB,sBAC5D,gBAAc,YAAY,OAAO,IAAK0D,GACrC1D,EAAA,cAAC,SACC,IAAK0D,EACL,UAAU,sBACV,MAAO,CAAE,gBAAiB,SAASA,CAAK,KAAM,GAE9C1D,EAAA,cAAC,WACC,QAAS,GAAG2D,EAAWD,CAAK,CAAC,GAC3BvC,IAAgB,QAAUmB,IAAsB,OAC5C,KAAKqB,EAAWrB,CAAiB,CAAC,IAClC,EACN,IAEAtC,EAAA,cAAC,SACC,UAAU,2BACV,KAAK,QACL,KAAK,cACL,MAAO0D,EACP,QAASvC,IAAgBuC,EACzB,SAAWV,GACT5B,EAAoB4B,EAAM,OAAO,KAA2B,EAEhE,CACF,CACF,CACD,CACH,EAEAhD,EAAA,cAAC,QAAK,QAAO,GAAC,MAAM,SAAS,QAAQ,WACnCA,EAAA,cAAC,QAAK,GAAG,IAAI,GAAG,mBAAmB,KAAK,IAAI,OAAO,SAAS,GAAG,KAAI,YAEnE,CACF,EAEAA,EAAA,cAAC,QAAK,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,kBAAgB,oBAC5D,gBAAc,UAAU,OAAO,IAAK4D,GACnC5D,EAAA,cAAC,QAAK,IAAK4D,EAAM,QAAO,GAAC,MAAM,SAAS,QAAQ,UAC9C5D,EAAA,cAAC,SACC,UAAU,sBACV,MAAO,CACL,gBACE4D,IAAS,OACL,SAASvB,CAAe,MACxBuB,IAAS,OACT,gBACA,SAASA,CAAI,MAGnB,OAAQA,IAAS,OAAS,cAAgB,MAC5C,GAEA5D,EAAA,cAAC,WACC,QAAS,GAAG2D,EAAWC,CAAI,CAAC,GAC1BA,IAAS,OAAS,KAAKD,EAAWtB,CAAe,CAAC,IAAM,EAC1D,IAEArC,EAAA,cAAC,SACC,UAAU,2BACV,KAAK,QACL,KAAK,YACL,MAAO4D,EACP,QAASvC,IAAcuC,EACvB,SAAWZ,GACT1B,EAAkB0B,EAAM,OAAO,KAAyB,EAE5D,CACF,CACF,CACF,CACD,CACH,EAEAhD,EAAA,cAAC,QAAK,GAAG,mBAAmB,GAAG,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG,KAAI,YAEnE,EAEAA,EAAA,cAAC,QAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,kBAAgB,oBAC1D,CAAC,QAAS,MAAM,EAAY,IAAKgC,GACjChC,EAAA,cAAC,SAAM,IAAKgC,EAAO,UAAU,0BAC3BhC,EAAA,cAAC,SACC,UAAU,8BACV,KAAK,QACL,KAAK,aACL,MAAOgC,EACP,QAASa,IAAuBb,EAChC,SAAWgB,GACTjB,EAAuBiB,EAAM,OAAO,KAAyB,EAEjE,EACAhD,EAAA,cAAC,QAAK,MAAM,SAAS,QAAQ,SAAS,OAAO,OAAO,IAAI,KACrDgC,IAAU,QACThC,EAAA,cAAC,OACC,MAAM,KACN,OAAO,KACP,QAAQ,YACR,KAAK,OACL,MAAM,6BACN,MAAO,CAAE,OAAQ,QAAS,GAE1BA,EAAA,cAAC,QACC,EAAE,2rDACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,CACH,EAEAA,EAAA,cAAC,OACC,MAAM,KACN,OAAO,KACP,QAAQ,YACR,KAAK,OACL,MAAM,6BACN,MAAO,CAAE,OAAQ,QAAS,GAE1BA,EAAA,cAAC,QACC,EAAE,u2GACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,CACH,EAEFA,EAAA,cAAC,QAAK,KAAK,IAAI,OAAO,UACnB2D,EAAW3B,CAAK,CACnB,CACF,CACF,CACD,CACH,EAEAhC,EAAA,cAAC,QAAK,GAAG,eAAe,GAAG,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG,KAAI,QAE/D,EAEAA,EAAA,cAAC,QAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,kBAAgB,gBAC3D,gBAAc,OAAO,OAAO,IAAKgC,GAChChC,EAAA,cAAC,QAAK,IAAKgC,EAAO,UAAU,SAAS,MAAM,UACzChC,EAAA,cAAC,SAAM,UAAU,0BACfA,EAAA,cAAC,SACC,UAAU,8BACV,KAAK,QACL,KAAK,SACL,GAAI,sBAAsBgC,CAAK,GAC/B,MAAOA,EACP,QAASP,IAAWO,EACpB,SAAWgB,GAAUtB,EAAesB,EAAM,OAAO,KAAsB,EACzE,EACAhD,EAAA,cAAC,SAAM,QAAO,GAAC,OAAQgC,GACrBhC,EAAA,cAAC,OACC,EAAE,IACF,MAAM,OACN,OAAO,OACP,MAAO,CACL,oBAAqBgC,IAAU,OAAS,MAAQ,kBAChD,gBACE,qEACF,UAAW,6BACX,WAAY,4BACd,EACF,CACF,CACF,EACAhC,EAAA,cAAC,OAAI,QAAO,GAAC,GAAG,KACdA,EAAA,cAAC,QAAK,QAAO,GAAC,KAAK,IAAI,MAAM,QAC3BA,EAAA,cAAC,SAAM,QAAS,sBAAsBgC,CAAK,IAAK2B,EAAW3B,CAAK,CAAE,CACpE,CACF,CACF,CACD,CACH,EAEAhC,EAAA,cAAC,QAAK,GAAG,gBAAgB,GAAG,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG,KAAI,SAEhE,EAEAA,EAAA,cAAC,QAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,kBAAgB,iBAC3D,gBAAc,QAAQ,OAAO,IAAKgC,GACjChC,EAAA,cAAC,SAAM,IAAKgC,EAAO,UAAU,0BAC3BhC,EAAA,cAAC,SACC,UAAU,8BACV,KAAK,QACL,KAAK,UACL,MAAOgC,EACP,QAASL,IAAYK,EACrB,SAAWgB,GAAUpB,EAAgBoB,EAAM,OAAO,KAAuB,EAC3E,EAEAhD,EAAA,cAAC,QAAK,MAAM,SAAS,QAAQ,SAAS,OAAO,QAC3CA,EAAA,cAAC,SAAM,QAAO,GAAC,QAASgC,GACtBhC,EAAA,cAAC,QAAK,MAAM,SAAS,QAAQ,UAC3BA,EAAA,cAAC,QAAK,KAAK,IAAI,OAAO,UACnB2D,EAAW3B,CAAK,CACnB,CACF,CACF,CACF,CACF,CACD,CACH,EAEAhC,EAAA,cAAC,QAAK,GAAG,IAAI,MAAM,SAAS,IAAI,KAC9BA,EAAA,cAAC,QAAK,GAAG,yBAAyB,GAAG,IAAI,KAAK,IAAI,OAAO,UAAS,kBAElE,EAEAA,EAAA,cAAC,UAAQ,KAAR,KACCA,EAAA,cAAC,UAAQ,QAAR,KACCA,EAAA,cAAC,cAAW,KAAK,IAAI,QAAQ,QAAQ,MAAM,QACzCA,EAAA,cAAC,kBAAe,MAAM,6CACpBA,EAAA,cAAC,OACC,MAAM,KACN,OAAO,KACP,QAAQ,YACR,KAAK,eACL,MAAM,8BAENA,EAAA,cAAC,QACC,EAAE,+zBACF,SAAS,UACT,SAAS,UACX,CACF,CACF,CACF,CACF,EAEAA,EAAA,cAAC,UAAQ,QAAR,CAAgB,KAAK,IAAI,MAAO,CAAE,SAAU,GAAI,EAAG,KAAK,MAAM,MAAM,UACnEA,EAAA,cAAC,QAAK,GAAG,IAAI,KAAK,KAAI,+FAGtB,CACF,CACF,CACF,EAEAA,EAAA,cAAC,QACC,QAAQ,IACR,IAAI,IACJ,GAAG,IACH,KAAK,QACL,kBAAgB,0BAEf,gBAAc,gBAAgB,OAAO,IAAKgC,GACzChC,EAAA,cAAC,SAAM,IAAKgC,EAAO,UAAU,0BAC3BhC,EAAA,cAAC,SACC,UAAU,8BACV,KAAK,QACL,KAAK,kBACL,MAAOgC,EACP,QAAST,IAAoBS,EAC7B,SAAWgB,GACTxB,EAAwBwB,EAAM,OAAO,KAA+B,EAExE,EACAhD,EAAA,cAAC,QAAK,MAAM,SAAS,QAAQ,SAAS,OAAO,OAAO,IAAI,KACrDgC,IAAU,QACThC,EAAA,cAAC,OACC,MAAM,KACN,OAAO,KACP,QAAQ,YACR,KAAK,OACL,MAAM,6BACN,MAAO,CAAE,OAAQ,QAAS,GAE1BA,EAAA,cAAC,QACC,EAAE,4aACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,CACH,EAEAA,EAAA,cAAC,OACC,MAAM,KACN,OAAO,KACP,QAAQ,YACR,KAAK,OACL,MAAM,6BACN,MAAO,CAAE,OAAQ,QAAS,GAE1BA,EAAA,cAAC,QACC,QAAQ,MACR,EAAE,6IACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,KACR,EAAE,kJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,MACR,EAAE,mJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,KACR,EAAE,kJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,MACR,EAAE,mJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,KACR,EAAE,mJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,MACR,EAAE,iJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,KACR,EAAE,iJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,MACR,EAAE,iJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,QAAQ,KACR,EAAE,gJACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,EACDA,EAAA,cAAC,QACC,EAAE,4aACF,KAAK,eACL,SAAS,UACT,SAAS,UACV,CACH,EAEFA,EAAA,cAAC,QAAK,KAAK,IAAI,OAAO,UACnB2D,EAAW3B,CAAK,CACnB,CACF,CACF,CACD,CACH,EAEAhC,EAAA,cAAC,UAAO,GAAG,IAAI,MAAO,CAAE,MAAO,MAAO,EAAG,QAASyC,GAC/CF,IAAc,SAAW,SAAW,YACvC,CACF,CACF,CACF,CACF,CAEJ,CACF,EACA3B,EAAe,YAAc,iBAG7B,SAASsB,IAAmB,CAC1B,MAAM2B,EAAM,SAAS,cAAc,OAAO,EAC1C,OAAAA,EAAI,YACF,SAAS,eACP,6KACF,CACF,EACA,SAAS,KAAK,YAAYA,CAAG,EAEtB,IAAM,CAEJ,OAAO,iBAAiB,SAAS,IAAI,EAG5C,WAAW,IAAM,CACf,SAAS,KAAK,YAAYA,CAAG,CAC/B,EAAG,CAAC,CACN,CACF,CAEA,SAASF,EAAWG,EAAgB,CAClC,OAAOA,EAAO,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAO,MAAM,CAAC,CACxD,CAEA,SAAS1B,GAA0BnB,EAA8B,CAC/D,MAAMkC,EAAO,SAAS,gBAChBY,EAAgBZ,EAAK,UAAU,SAAS,aAAa,EACrDa,EAAeb,EAAK,UAAU,SAAS,YAAY,EACnDc,EAAWd,EAAK,UAAU,SAAS,OAAO,EAC1Ce,EAAUf,EAAK,UAAU,SAAS,MAAM,GAE1CY,GAAiBC,KACnBb,EAAK,UAAU,OAAO,cAAe,YAAY,EACjDA,EAAK,MAAM,YAAclC,EACzBkC,EAAK,UAAU,IAAI,GAAGlC,CAAU,QAAQ,IAGtCgD,GAAYC,KACdf,EAAK,UAAU,OAAO,QAAS,MAAM,EACrCA,EAAK,MAAM,YAAclC,EACzBkC,EAAK,UAAU,IAAIlC,CAAU,GAG3B,CAAC8C,GAAiB,CAACC,GAAgB,CAACC,GAAY,CAACC,IACnDf,EAAK,MAAM,YAAclC,EACzBkC,EAAK,UAAU,IAAIlC,CAAU,EAEjC", "names": ["theme_panel_exports", "__export", "ThemePanel", "__toCommonJS", "React", "import_internal", "import__", "import_theme", "import_inert", "import_get_matching_gray_color", "import_theme_props", "defaultOpen", "props", "forwardedRef", "open", "<PERSON><PERSON><PERSON>", "ThemePanelImpl", "onOpenChange", "onAppearanceChangeProp", "panelProps", "themeContext", "appearance", "onAppearanceChange", "accentColor", "onAccentColorChange", "grayColor", "onGrayColorChange", "panelBackground", "onPanelBackgroundChange", "radius", "onRadiusChange", "scaling", "onScalingChange", "hasOnAppearanceChangeProp", "handleAppearanceChangeProp", "handleAppearanceChange", "value", "cleanup", "disableAnimation", "setResolvedAppearance", "updateRootAppearanceClass", "autoMatchedGray", "resolvedGrayColor", "copyState", "setCopyState", "handleCopyThemeConfig", "theme", "key", "textToCopy", "resolvedAppearance", "keyboardInputElement", "handleKeydown", "event", "isModifierActive", "isKeyboardInputActive", "root", "body", "update", "hasDarkClass", "classNameObserver", "mutations", "mutation", "color", "upperFirst", "gray", "css", "string", "hasLightTheme", "hasDarkTheme", "hasLight", "hasDark"]}