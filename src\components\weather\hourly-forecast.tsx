'use client';

import React from 'react';
import { Box, Text, Flex } from '@radix-ui/themes';
import { ForecastCard } from '@/components/ui';
import { SmallWeatherIcon } from '@/lib/weather-icons';
import type { HourlyForecastProps } from '@/types';

export function HourlyForecast({
  hourly,
  units,
  hoursToShow = 12,
  className = '',
}: HourlyForecastProps): React.ReactElement {
  const temperatureSymbol = units.temp === "celsius" ? "°" : "°";

  return (
    <Box className={className}>
      <Text weight="medium" mb="2">
        Next {hoursToShow} hours
      </Text>
      <Flex mt="2" gap="2" wrap="wrap">
        {hourly.time.slice(0, hoursToShow).map((time, index) => {
          const date = new Date(time);
          const timeString = date.toLocaleTimeString(undefined, { 
            hour: "2-digit", 
            minute: "2-digit" 
          });
          const temperature = Math.round(hourly.temperature_2m[index]);
          const weatherCode = hourly.weather_code[index];

          return (
            <ForecastCard
              key={time}
              time={timeString}
              temperature={`${temperature}${temperatureSymbol}`}
              icon={
                <SmallWeatherIcon 
                  code={weatherCode} 
                  isDay={1} // Could be enhanced to determine day/night based on time
                />
              }
            />
          );
        })}
      </Flex>
    </Box>
  );
}
