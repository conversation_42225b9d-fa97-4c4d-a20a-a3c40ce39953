'use client';

import { useState, useEffect, useCallback } from 'react';
import { fetchWeather, fetchAQI } from '@/lib/openmeteo';
import type { UseWeatherDataReturn, Location, WeatherData, AQIData, Units } from '@/types';

export function useWeatherData(
  location: Location | null,
  units: Units
): UseWeatherDataReturn {
  const [data, setData] = useState<WeatherData | null>(null);
  const [aqi, setAqi] = useState<AQIData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!location) {
      setData(null);
      setAqi(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Fetch weather data
      const weatherData = await fetchWeather({
        latitude: location.latitude,
        longitude: location.longitude,
        tempUnit: units.temp,
        windUnit: units.wind,
      });
      
      setData(weatherData);

      // Try to fetch AQI data (optional)
      try {
        const aqiData = await fetchAQI({
          latitude: location.latitude,
          longitude: location.longitude,
        });
        setAqi(aqiData);
      } catch (aqiError) {
        console.warn('Failed to fetch AQI data:', aqiError);
        setAqi(null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch weather data';
      setError(errorMessage);
      setData(null);
      setAqi(null);
    } finally {
      setLoading(false);
    }
  }, [location, units]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    aqi,
    loading,
    error,
    refetch,
  };
}
