{"version": 3, "sources": ["../../../src/components/strong.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Slot } from 'radix-ui';\n\nimport { extractProps } from '../helpers/extract-props.js';\nimport { strongPropDefs } from './strong.props.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype StrongElement = React.ElementRef<'strong'>;\ntype StrongOwnProps = GetPropDefTypes<typeof strongPropDefs>;\ninterface StrongProps extends ComponentPropsWithout<'strong', RemovedProps>, StrongOwnProps {}\nconst Strong = React.forwardRef<StrongElement, StrongProps>((props, forwardedRef) => {\n  const { asChild, className, ...strongProps } = extractProps(props, strongPropDefs);\n  const Comp = asChild ? Slot.Root : 'strong';\n  return (\n    <Comp {...strongProps} ref={forwardedRef} className={classNames('rt-Strong', className)} />\n  );\n});\nStrong.displayName = 'Strong';\n\nexport { Strong };\nexport type { StrongProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,QAAAC,MAAY,WAErB,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,kBAAAC,MAAsB,oBAQ/B,MAAMC,EAASL,EAAM,WAAuC,CAACM,EAAOC,IAAiB,CACnF,KAAM,CAAE,QAAAC,EAAS,UAAAC,EAAW,GAAGC,CAAY,EAAIP,EAAaG,EAAOF,CAAc,EAC3EO,EAAOH,EAAUN,EAAK,KAAO,SACnC,OACEF,EAAA,cAACW,EAAA,CAAM,GAAGD,EAAa,IAAKH,EAAc,UAAWN,EAAW,YAAaQ,CAAS,EAAG,CAE7F,CAAC,EACDJ,EAAO,YAAc", "names": ["React", "classNames", "Slot", "extractProps", "strongPropDefs", "Strong", "props", "forwardedRef", "<PERSON><PERSON><PERSON><PERSON>", "className", "strongProps", "Comp"]}