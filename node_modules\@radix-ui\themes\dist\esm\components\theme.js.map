{"version": 3, "sources": ["../../../src/components/theme.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Direction, Slot, Tooltip as TooltipPrimitive } from 'radix-ui';\n\nimport { getMatchingGrayColor } from '../helpers/get-matching-gray-color.js';\nimport { themePropDefs } from './theme.props.js';\n\nimport type { ThemeOwnProps } from './theme.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\n\nconst noop = () => {};\n\ntype ThemeAppearance = (typeof themePropDefs.appearance.values)[number];\ntype ThemeAccentColor = (typeof themePropDefs.accentColor.values)[number];\ntype ThemeGrayColor = (typeof themePropDefs.grayColor.values)[number];\ntype ThemePanelBackground = (typeof themePropDefs.panelBackground.values)[number];\ntype ThemeRadius = (typeof themePropDefs.radius.values)[number];\ntype ThemeScaling = (typeof themePropDefs.scaling.values)[number];\n\ninterface ThemeChangeHandlers {\n  onAppearanceChange: (appearance: ThemeAppearance) => void;\n  onAccentColorChange: (accentColor: ThemeAccentColor) => void;\n  onGrayColorChange: (grayColor: ThemeGrayColor) => void;\n  onPanelBackgroundChange: (panelBackground: ThemePanelBackground) => void;\n  onRadiusChange: (radius: ThemeRadius) => void;\n  onScalingChange: (scaling: ThemeScaling) => void;\n}\n\ninterface ThemeContextValue extends ThemeChangeHandlers {\n  appearance: ThemeAppearance;\n  accentColor: ThemeAccentColor;\n  grayColor: ThemeGrayColor;\n  resolvedGrayColor: ThemeGrayColor;\n  panelBackground: ThemePanelBackground;\n  radius: ThemeRadius;\n  scaling: ThemeScaling;\n}\nconst ThemeContext = React.createContext<ThemeContextValue | undefined>(undefined);\n\nfunction useThemeContext() {\n  const context = React.useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('`useThemeContext` must be used within a `Theme`');\n  }\n  return context;\n}\n\ninterface ThemeProps extends ThemeImplPublicProps {}\nconst Theme = React.forwardRef<ThemeImplElement, ThemeProps>((props, forwardedRef) => {\n  const context = React.useContext(ThemeContext);\n  const isRoot = context === undefined;\n  if (isRoot) {\n    return (\n      <TooltipPrimitive.Provider delayDuration={200}>\n        <Direction.Provider dir=\"ltr\">\n          <ThemeRoot {...props} ref={forwardedRef} />\n        </Direction.Provider>\n      </TooltipPrimitive.Provider>\n    );\n  }\n  return <ThemeImpl {...props} ref={forwardedRef} />;\n});\nTheme.displayName = 'Theme';\n\nconst ThemeRoot = React.forwardRef<ThemeImplElement, ThemeImplPublicProps>(\n  (props, forwardedRef) => {\n    const {\n      appearance: appearanceProp = themePropDefs.appearance.default,\n      accentColor: accentColorProp = themePropDefs.accentColor.default,\n      grayColor: grayColorProp = themePropDefs.grayColor.default,\n      panelBackground: panelBackgroundProp = themePropDefs.panelBackground.default,\n      radius: radiusProp = themePropDefs.radius.default,\n      scaling: scalingProp = themePropDefs.scaling.default,\n      hasBackground = themePropDefs.hasBackground.default,\n      ...rootProps\n    } = props;\n    const [appearance, setAppearance] = React.useState(appearanceProp);\n    React.useEffect(() => setAppearance(appearanceProp), [appearanceProp]);\n\n    const [accentColor, setAccentColor] = React.useState(accentColorProp);\n    React.useEffect(() => setAccentColor(accentColorProp), [accentColorProp]);\n\n    const [grayColor, setGrayColor] = React.useState(grayColorProp);\n    React.useEffect(() => setGrayColor(grayColorProp), [grayColorProp]);\n\n    const [panelBackground, setPanelBackground] = React.useState(panelBackgroundProp);\n    React.useEffect(() => setPanelBackground(panelBackgroundProp), [panelBackgroundProp]);\n\n    const [radius, setRadius] = React.useState(radiusProp);\n    React.useEffect(() => setRadius(radiusProp), [radiusProp]);\n\n    const [scaling, setScaling] = React.useState(scalingProp);\n    React.useEffect(() => setScaling(scalingProp), [scalingProp]);\n\n    return (\n      <ThemeImpl\n        {...rootProps}\n        ref={forwardedRef}\n        isRoot\n        hasBackground={hasBackground}\n        //\n        appearance={appearance}\n        accentColor={accentColor}\n        grayColor={grayColor}\n        panelBackground={panelBackground}\n        radius={radius}\n        scaling={scaling}\n        //\n        onAppearanceChange={setAppearance}\n        onAccentColorChange={setAccentColor}\n        onGrayColorChange={setGrayColor}\n        onPanelBackgroundChange={setPanelBackground}\n        onRadiusChange={setRadius}\n        onScalingChange={setScaling}\n      />\n    );\n  }\n);\nThemeRoot.displayName = 'ThemeRoot';\n\ntype ThemeImplElement = React.ElementRef<'div'>;\ninterface ThemeImplProps extends ThemeImplPublicProps, ThemeImplPrivateProps {}\ninterface ThemeImplPublicProps\n  extends ComponentPropsWithout<'div', RemovedProps | 'dir'>,\n    ThemeOwnProps {}\ninterface ThemeImplPrivateProps extends Partial<ThemeChangeHandlers> {\n  isRoot?: boolean;\n}\nconst ThemeImpl = React.forwardRef<ThemeImplElement, ThemeImplProps>((props, forwardedRef) => {\n  const context = React.useContext(ThemeContext);\n  const {\n    asChild,\n    isRoot,\n    hasBackground: hasBackgroundProp,\n    //\n    appearance = context?.appearance ?? themePropDefs.appearance.default,\n    accentColor = context?.accentColor ?? themePropDefs.accentColor.default,\n    grayColor = context?.resolvedGrayColor ?? themePropDefs.grayColor.default,\n    panelBackground = context?.panelBackground ?? themePropDefs.panelBackground.default,\n    radius = context?.radius ?? themePropDefs.radius.default,\n    scaling = context?.scaling ?? themePropDefs.scaling.default,\n    //\n    onAppearanceChange = noop,\n    onAccentColorChange = noop,\n    onGrayColorChange = noop,\n    onPanelBackgroundChange = noop,\n    onRadiusChange = noop,\n    onScalingChange = noop,\n    //\n    ...themeProps\n  } = props;\n  const Comp = asChild ? Slot.Root : 'div';\n  const resolvedGrayColor = grayColor === 'auto' ? getMatchingGrayColor(accentColor) : grayColor;\n  const isExplicitAppearance = props.appearance === 'light' || props.appearance === 'dark';\n  const hasBackground =\n    hasBackgroundProp === undefined ? isRoot || isExplicitAppearance : hasBackgroundProp;\n  return (\n    <ThemeContext.Provider\n      value={React.useMemo(\n        () => ({\n          appearance,\n          accentColor,\n          grayColor,\n          resolvedGrayColor,\n          panelBackground,\n          radius,\n          scaling,\n          //\n          onAppearanceChange,\n          onAccentColorChange,\n          onGrayColorChange,\n          onPanelBackgroundChange,\n          onRadiusChange,\n          onScalingChange,\n        }),\n        [\n          appearance,\n          accentColor,\n          grayColor,\n          resolvedGrayColor,\n          panelBackground,\n          radius,\n          scaling,\n          //\n          onAppearanceChange,\n          onAccentColorChange,\n          onGrayColorChange,\n          onPanelBackgroundChange,\n          onRadiusChange,\n          onScalingChange,\n        ]\n      )}\n    >\n      <Comp\n        data-is-root-theme={isRoot ? 'true' : 'false'}\n        data-accent-color={accentColor}\n        data-gray-color={resolvedGrayColor}\n        // for nested `Theme` background\n        data-has-background={hasBackground ? 'true' : 'false'}\n        data-panel-background={panelBackground}\n        data-radius={radius}\n        data-scaling={scaling}\n        ref={forwardedRef}\n        {...themeProps}\n        className={classNames(\n          'radix-themes',\n          {\n            light: appearance === 'light',\n            dark: appearance === 'dark',\n          },\n          themeProps.className\n        )}\n      />\n    </ThemeContext.Provider>\n  );\n});\nThemeImpl.displayName = 'ThemeImpl';\n\nexport { Theme, ThemeContext, useThemeContext };\nexport type { ThemeProps };\n"], "mappings": "aAEA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,aAAAC,EAAW,QAAAC,EAAM,WAAWC,MAAwB,WAE7D,OAAS,wBAAAC,MAA4B,wCACrC,OAAS,iBAAAC,MAAqB,mBAK9B,MAAMC,EAAO,IAAM,CAAC,EA2BdC,EAAeR,EAAM,cAA6C,MAAS,EAEjF,SAASS,GAAkB,CACzB,MAAMC,EAAUV,EAAM,WAAWQ,CAAY,EAC7C,GAAIE,IAAY,OACd,MAAM,IAAI,MAAM,iDAAiD,EAEnE,OAAOA,CACT,CAGA,MAAMC,EAAQX,EAAM,WAAyC,CAACY,EAAOC,IACnDb,EAAM,WAAWQ,CAAY,IAClB,OAGvBR,EAAA,cAACI,EAAiB,SAAjB,CAA0B,cAAe,KACxCJ,EAAA,cAACE,EAAU,SAAV,CAAmB,IAAI,OACtBF,EAAA,cAACc,EAAA,CAAW,GAAGF,EAAO,IAAKC,EAAc,CAC3C,CACF,EAGGb,EAAA,cAACe,EAAA,CAAW,GAAGH,EAAO,IAAKC,EAAc,CACjD,EACDF,EAAM,YAAc,QAEpB,MAAMG,EAAYd,EAAM,WACtB,CAACY,EAAOC,IAAiB,CACvB,KAAM,CACJ,WAAYG,EAAiBV,EAAc,WAAW,QACtD,YAAaW,EAAkBX,EAAc,YAAY,QACzD,UAAWY,EAAgBZ,EAAc,UAAU,QACnD,gBAAiBa,EAAsBb,EAAc,gBAAgB,QACrE,OAAQc,EAAad,EAAc,OAAO,QAC1C,QAASe,EAAcf,EAAc,QAAQ,QAC7C,cAAAgB,EAAgBhB,EAAc,cAAc,QAC5C,GAAGiB,CACL,EAAIX,EACE,CAACY,EAAYC,CAAa,EAAIzB,EAAM,SAASgB,CAAc,EACjEhB,EAAM,UAAU,IAAMyB,EAAcT,CAAc,EAAG,CAACA,CAAc,CAAC,EAErE,KAAM,CAACU,EAAaC,CAAc,EAAI3B,EAAM,SAASiB,CAAe,EACpEjB,EAAM,UAAU,IAAM2B,EAAeV,CAAe,EAAG,CAACA,CAAe,CAAC,EAExE,KAAM,CAACW,EAAWC,CAAY,EAAI7B,EAAM,SAASkB,CAAa,EAC9DlB,EAAM,UAAU,IAAM6B,EAAaX,CAAa,EAAG,CAACA,CAAa,CAAC,EAElE,KAAM,CAACY,EAAiBC,CAAkB,EAAI/B,EAAM,SAASmB,CAAmB,EAChFnB,EAAM,UAAU,IAAM+B,EAAmBZ,CAAmB,EAAG,CAACA,CAAmB,CAAC,EAEpF,KAAM,CAACa,EAAQC,CAAS,EAAIjC,EAAM,SAASoB,CAAU,EACrDpB,EAAM,UAAU,IAAMiC,EAAUb,CAAU,EAAG,CAACA,CAAU,CAAC,EAEzD,KAAM,CAACc,EAASC,CAAU,EAAInC,EAAM,SAASqB,CAAW,EACxD,OAAArB,EAAM,UAAU,IAAMmC,EAAWd,CAAW,EAAG,CAACA,CAAW,CAAC,EAG1DrB,EAAA,cAACe,EAAA,CACE,GAAGQ,EACJ,IAAKV,EACL,OAAM,GACN,cAAeS,EAEf,WAAYE,EACZ,YAAaE,EACb,UAAWE,EACX,gBAAiBE,EACjB,OAAQE,EACR,QAASE,EAET,mBAAoBT,EACpB,oBAAqBE,EACrB,kBAAmBE,EACnB,wBAAyBE,EACzB,eAAgBE,EAChB,gBAAiBE,EACnB,CAEJ,CACF,EACArB,EAAU,YAAc,YAUxB,MAAMC,EAAYf,EAAM,WAA6C,CAACY,EAAOC,IAAiB,CAC5F,MAAMH,EAAUV,EAAM,WAAWQ,CAAY,EACvC,CACJ,QAAA4B,EACA,OAAAC,EACA,cAAeC,EAEf,WAAAd,EAAad,GAAS,YAAcJ,EAAc,WAAW,QAC7D,YAAAoB,EAAchB,GAAS,aAAeJ,EAAc,YAAY,QAChE,UAAAsB,EAAYlB,GAAS,mBAAqBJ,EAAc,UAAU,QAClE,gBAAAwB,EAAkBpB,GAAS,iBAAmBJ,EAAc,gBAAgB,QAC5E,OAAA0B,EAAStB,GAAS,QAAUJ,EAAc,OAAO,QACjD,QAAA4B,EAAUxB,GAAS,SAAWJ,EAAc,QAAQ,QAEpD,mBAAAiC,EAAqBhC,EACrB,oBAAAiC,EAAsBjC,EACtB,kBAAAkC,EAAoBlC,EACpB,wBAAAmC,EAA0BnC,EAC1B,eAAAoC,EAAiBpC,EACjB,gBAAAqC,EAAkBrC,EAElB,GAAGsC,CACL,EAAIjC,EACEkC,EAAOV,EAAUjC,EAAK,KAAO,MAC7B4C,EAAoBnB,IAAc,OAASvB,EAAqBqB,CAAW,EAAIE,EAC/EoB,EAAuBpC,EAAM,aAAe,SAAWA,EAAM,aAAe,OAC5EU,EACJgB,IAAsB,OAAYD,GAAUW,EAAuBV,EACrE,OACEtC,EAAA,cAACQ,EAAa,SAAb,CACC,MAAOR,EAAM,QACX,KAAO,CACL,WAAAwB,EACA,YAAAE,EACA,UAAAE,EACA,kBAAAmB,EACA,gBAAAjB,EACA,OAAAE,EACA,QAAAE,EAEA,mBAAAK,EACA,oBAAAC,EACA,kBAAAC,EACA,wBAAAC,EACA,eAAAC,EACA,gBAAAC,CACF,GACA,CACEpB,EACAE,EACAE,EACAmB,EACAjB,EACAE,EACAE,EAEAK,EACAC,EACAC,EACAC,EACAC,EACAC,CACF,CACF,GAEA5C,EAAA,cAAC8C,EAAA,CACC,qBAAoBT,EAAS,OAAS,QACtC,oBAAmBX,EACnB,kBAAiBqB,EAEjB,sBAAqBzB,EAAgB,OAAS,QAC9C,wBAAuBQ,EACvB,cAAaE,EACb,eAAcE,EACd,IAAKrB,EACJ,GAAGgC,EACJ,UAAW5C,EACT,eACA,CACE,MAAOuB,IAAe,QACtB,KAAMA,IAAe,MACvB,EACAqB,EAAW,SACb,EACF,CACF,CAEJ,CAAC,EACD9B,EAAU,YAAc", "names": ["React", "classNames", "Direction", "Slot", "TooltipPrimitive", "getMatchingGrayColor", "themePropDefs", "noop", "ThemeContext", "useThemeContext", "context", "Theme", "props", "forwardedRef", "ThemeRoot", "ThemeImpl", "appearanceProp", "accentColorProp", "grayColorProp", "panelBackgroundProp", "radiusProp", "scalingProp", "hasBackground", "rootProps", "appearance", "set<PERSON><PERSON><PERSON>ce", "accentColor", "setAccentColor", "grayColor", "setGrayColor", "panelBackground", "setPanelBackground", "radius", "setRadius", "scaling", "setScaling", "<PERSON><PERSON><PERSON><PERSON>", "isRoot", "hasBackgroundProp", "onAppearanceChange", "onAccentColorChange", "onGrayColorChange", "onPanelBackgroundChange", "onRadiusChange", "onScalingChange", "themeProps", "Comp", "resolvedGrayColor", "isExplicitAppearance"]}