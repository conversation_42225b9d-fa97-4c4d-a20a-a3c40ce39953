import * as React from 'react';
import { themePropDefs } from './theme.props.js';
import type { ThemeOwnProps } from './theme.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
type ThemeAppearance = (typeof themePropDefs.appearance.values)[number];
type ThemeAccentColor = (typeof themePropDefs.accentColor.values)[number];
type ThemeGrayColor = (typeof themePropDefs.grayColor.values)[number];
type ThemePanelBackground = (typeof themePropDefs.panelBackground.values)[number];
type ThemeRadius = (typeof themePropDefs.radius.values)[number];
type ThemeScaling = (typeof themePropDefs.scaling.values)[number];
interface ThemeChangeHandlers {
    onAppearanceChange: (appearance: ThemeAppearance) => void;
    onAccentColorChange: (accentColor: ThemeAccentColor) => void;
    onGrayColorChange: (grayColor: ThemeGrayColor) => void;
    onPanelBackgroundChange: (panelBackground: ThemePanelBackground) => void;
    onRadiusChange: (radius: ThemeRadius) => void;
    onScalingChange: (scaling: ThemeScaling) => void;
}
interface ThemeContextValue extends ThemeChangeHandlers {
    appearance: ThemeAppearance;
    accentColor: ThemeAccentColor;
    grayColor: ThemeGrayColor;
    resolvedGrayColor: ThemeGrayColor;
    panelBackground: ThemePanelBackground;
    radius: ThemeRadius;
    scaling: ThemeScaling;
}
declare const ThemeContext: React.Context<ThemeContextValue | undefined>;
declare function useThemeContext(): ThemeContextValue;
interface ThemeProps extends ThemeImplPublicProps {
}
declare const Theme: React.ForwardRefExoticComponent<ThemeProps & React.RefAttributes<HTMLDivElement>>;
interface ThemeImplPublicProps extends ComponentPropsWithout<'div', RemovedProps | 'dir'>, ThemeOwnProps {
}
export { Theme, ThemeContext, useThemeContext };
export type { ThemeProps };
//# sourceMappingURL=theme.d.ts.map