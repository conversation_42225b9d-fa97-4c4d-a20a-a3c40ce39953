import * as React from 'react';
import { strongPropDefs } from './strong.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type StrongOwnProps = GetPropDefTypes<typeof strongPropDefs>;
interface StrongProps extends ComponentPropsWithout<'strong', RemovedProps>, StrongOwnProps {
}
declare const Strong: React.ForwardRefExoticComponent<StrongProps & React.RefAttributes<HTMLElement>>;
export { Strong };
export type { StrongProps };
//# sourceMappingURL=strong.d.ts.map