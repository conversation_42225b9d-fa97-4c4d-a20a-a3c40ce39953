{"version": 3, "sources": ["../../../src/components/spinner.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\n\nimport { Flex } from './flex.js';\nimport { spinnerPropDefs } from './spinner.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype SpinnerElement = React.ElementRef<'span'>;\ntype SpinnerOwnProps = GetPropDefTypes<typeof spinnerPropDefs>;\ninterface SpinnerProps\n  extends ComponentPropsWithout<'span', RemovedProps>,\n    MarginProps,\n    SpinnerOwnProps {}\nconst Spinner = React.forwardRef<SpinnerElement, SpinnerProps>((props, forwardedRef) => {\n  const { className, children, loading, ...spinnerProps } = extractProps(\n    props,\n    spinnerPropDefs,\n    marginPropDefs\n  );\n\n  if (!loading) return children;\n\n  const spinner = (\n    <span {...spinnerProps} ref={forwardedRef} className={classNames('rt-Spinner', className)}>\n      <span className=\"rt-SpinnerLeaf\" />\n      <span className=\"rt-SpinnerLeaf\" />\n      <span className=\"rt-SpinnerLeaf\" />\n      <span className=\"rt-SpinnerLeaf\" />\n      <span className=\"rt-SpinnerLeaf\" />\n      <span className=\"rt-SpinnerLeaf\" />\n      <span className=\"rt-SpinnerLeaf\" />\n      <span className=\"rt-SpinnerLeaf\" />\n    </span>\n  );\n\n  if (children === undefined) return spinner;\n\n  return (\n    <Flex asChild position=\"relative\" align=\"center\" justify=\"center\">\n      <span>\n        {/**\n         * `display: contents` removes the content from the accessibility tree in some browsers,\n         * so we force remove it with `aria-hidden`\n         */}\n        <span aria-hidden style={{ display: 'contents', visibility: 'hidden' }} inert={undefined}>\n          {children}\n        </span>\n\n        <Flex asChild align=\"center\" justify=\"center\" position=\"absolute\" inset=\"0\">\n          <span>{spinner}</span>\n        </Flex>\n      </span>\n    </Flex>\n  );\n});\nSpinner.displayName = 'Spinner';\n\nexport { Spinner };\nexport type { SpinnerProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aAEvB,OAAS,QAAAC,MAAY,YACrB,OAAS,mBAAAC,MAAuB,qBAChC,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,kBAAAC,MAAsB,2BAY/B,MAAMC,EAAUN,EAAM,WAAyC,CAACO,EAAOC,IAAiB,CACtF,KAAM,CAAE,UAAAC,EAAW,SAAAC,EAAU,QAAAC,EAAS,GAAGC,CAAa,EAAIR,EACxDG,EACAJ,EACAE,CACF,EAEA,GAAI,CAACM,EAAS,OAAOD,EAErB,MAAMG,EACJb,EAAA,cAAC,QAAM,GAAGY,EAAc,IAAKJ,EAAc,UAAWP,EAAW,aAAcQ,CAAS,GACtFT,EAAA,cAAC,QAAK,UAAU,iBAAiB,EACjCA,EAAA,cAAC,QAAK,UAAU,iBAAiB,EACjCA,EAAA,cAAC,QAAK,UAAU,iBAAiB,EACjCA,EAAA,cAAC,QAAK,UAAU,iBAAiB,EACjCA,EAAA,cAAC,QAAK,UAAU,iBAAiB,EACjCA,EAAA,cAAC,QAAK,UAAU,iBAAiB,EACjCA,EAAA,cAAC,QAAK,UAAU,iBAAiB,EACjCA,EAAA,cAAC,QAAK,UAAU,iBAAiB,CACnC,EAGF,OAAIU,IAAa,OAAkBG,EAGjCb,EAAA,cAACE,EAAA,CAAK,QAAO,GAAC,SAAS,WAAW,MAAM,SAAS,QAAQ,UACvDF,EAAA,cAAC,YAKCA,EAAA,cAAC,QAAK,cAAW,GAAC,MAAO,CAAE,QAAS,WAAY,WAAY,QAAS,EAAG,MAAO,QAC5EU,CACH,EAEAV,EAAA,cAACE,EAAA,CAAK,QAAO,GAAC,MAAM,SAAS,QAAQ,SAAS,SAAS,WAAW,MAAM,KACtEF,EAAA,cAAC,YAAMa,CAAQ,CACjB,CACF,CACF,CAEJ,CAAC,EACDP,EAAQ,YAAc", "names": ["React", "classNames", "Flex", "spinnerPropDefs", "extractProps", "marginPropDefs", "Spinner", "props", "forwardedRef", "className", "children", "loading", "spinnerProps", "spinner"]}