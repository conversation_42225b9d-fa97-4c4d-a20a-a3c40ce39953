'use client';

import React from 'react';
import { Card, Flex, Text, Button } from '@radix-ui/themes';
import { AlertCircle, RefreshCw } from 'lucide-react';
import type { ErrorStateProps } from '@/types';

export function ErrorState({
  message,
  onRetry,
  className = '',
}: ErrorStateProps): React.ReactElement {
  return (
    <Card className={`border-red-200 bg-red-50 ${className}`}>
      <Flex direction="column" align="center" gap="3" p="4">
        <AlertCircle size={32} className="text-red-500" />
        <Text color="red" align="center">
          {message}
        </Text>
        {onRetry && (
          <Button 
            variant="soft" 
            color="red" 
            onClick={onRetry}
            size="2"
          >
            <RefreshCw size={16} />
            Try Again
          </Button>
        )}
      </Flex>
    </Card>
  );
}
