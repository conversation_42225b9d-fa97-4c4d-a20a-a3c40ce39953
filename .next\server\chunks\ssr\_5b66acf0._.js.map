{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/weather-app/lib/openmeteo.js"], "sourcesContent": ["// Open-Meteo helpers (JS only)\n// Geocoding, Weather, AQI endpoints and small utilities\n\nconst BASE_GEO = \"https://geocoding-api.open-meteo.com/v1/search\";\nconst BASE_WEATHER = \"https://api.open-meteo.com/v1/forecast\";\nconst BASE_AQI = \"https://air-quality-api.open-meteo.com/v1/air-quality\";\n\nexport async function geocodeCity(query, { count = 5 } = {}) {\n  if (!query) return [];\n  const url = new URL(BASE_GEO);\n  url.searchParams.set(\"name\", query);\n  url.searchParams.set(\"count\", String(count));\n  url.searchParams.set(\"language\", \"en\");\n  url.searchParams.set(\"format\", \"json\");\n\n  const res = await fetch(url.toString(), { cache: \"no-store\" });\n  if (!res.ok) throw new Error(\"Geocoding failed\");\n  const data = await res.json();\n  return data?.results || [];\n}\n\nexport async function geocodePostal(postal, country) {\n  if (!postal) return [];\n  const url = new URL(BASE_GEO);\n  url.searchParams.set(\"postal_code\", postal);\n  if (country) url.searchParams.set(\"country\", country);\n  url.searchParams.set(\"count\", \"5\");\n  url.searchParams.set(\"language\", \"en\");\n  url.searchParams.set(\"format\", \"json\");\n  const res = await fetch(url.toString(), { cache: \"no-store\" });\n  if (!res.ok) throw new Error(\"Postal geocoding failed\");\n  const data = await res.json();\n  return data?.results || [];\n}\n\nexport function buildWeatherURL({\n  latitude,\n  longitude,\n  tempUnit = \"celsius\", // celsius | fahrenheit\n  windUnit = \"kmh\", // kmh | mph | ms | kn\n  hours = 24,\n}) {\n  const url = new URL(BASE_WEATHER);\n  url.searchParams.set(\"latitude\", String(latitude));\n  url.searchParams.set(\"longitude\", String(longitude));\n  url.searchParams.set(\n    \"current\",\n    [\n      \"temperature_2m\",\n      \"apparent_temperature\",\n      \"weather_code\",\n      \"relative_humidity_2m\",\n      \"wind_speed_10m\",\n      \"pressure_msl\",\n      \"is_day\",\n    ].join(\",\")\n  );\n  url.searchParams.set(\"hourly\", [\"temperature_2m\", \"weather_code\"].join(\",\"));\n  url.searchParams.set(\n    \"daily\",\n    [\n      \"temperature_2m_max\",\n      \"temperature_2m_min\",\n      \"sunrise\",\n      \"sunset\",\n      \"weather_code\",\n    ].join(\",\")\n  );\n  url.searchParams.set(\"timezone\", \"auto\");\n  url.searchParams.set(\"forecast_days\", \"7\");\n  url.searchParams.set(\"past_hours\", \"0\");\n  url.searchParams.set(\"future_hours\", String(hours));\n  url.searchParams.set(\"temperature_unit\", tempUnit);\n  url.searchParams.set(\"windspeed_unit\", windUnit);\n  return url.toString();\n}\n\nexport async function fetchWeather(opts) {\n  const url = buildWeatherURL(opts);\n  const res = await fetch(url, { cache: \"no-store\" });\n  if (!res.ok) throw new Error(\"Weather fetch failed\");\n  return res.json();\n}\n\nexport function buildAQIURL({ latitude, longitude }) {\n  const url = new URL(BASE_AQI);\n  url.searchParams.set(\"latitude\", String(latitude));\n  url.searchParams.set(\"longitude\", String(longitude));\n  url.searchParams.set(\"hourly\", [\"pm10\", \"pm2_5\", \"us_aqi\"].join(\",\"));\n  url.searchParams.set(\"timezone\", \"auto\");\n  url.searchParams.set(\"past_hours\", \"0\");\n  url.searchParams.set(\"forecast_hours\", \"24\");\n  return url.toString();\n}\n\nexport async function fetchAQI(opts) {\n  const res = await fetch(buildAQIURL(opts), { cache: \"no-store\" });\n  if (!res.ok) throw new Error(\"AQI fetch failed\");\n  return res.json();\n}\n\nexport function weatherCodeToText(code) {\n  const map = {\n    0: \"Clear sky\",\n    1: \"Mainly clear\",\n    2: \"Partly cloudy\",\n    3: \"Overcast\",\n    45: \"Fog\",\n    48: \"Depositing rime fog\",\n    51: \"Light drizzle\",\n    53: \"Moderate drizzle\",\n    55: \"Dense drizzle\",\n    56: \"Light freezing drizzle\",\n    57: \"Dense freezing drizzle\",\n    61: \"Slight rain\",\n    63: \"Moderate rain\",\n    65: \"Heavy rain\",\n    66: \"Light freezing rain\",\n    67: \"Heavy freezing rain\",\n    71: \"Slight snow\",\n    73: \"Moderate snow\",\n    75: \"Heavy snow\",\n    77: \"Snow grains\",\n    80: \"Slight rain showers\",\n    81: \"Moderate rain showers\",\n    82: \"Violent rain showers\",\n    85: \"Slight snow showers\",\n    86: \"Heavy snow showers\",\n    95: \"Thunderstorm\",\n    96: \"Thunderstorm with slight hail\",\n    99: \"Thunderstorm with heavy hail\",\n  };\n  return map[code] || \"Unknown\";\n}\n\nexport function weatherCodeToEmoji(code, isDay = 1) {\n  const sun = \"☀️\";\n  const moon = \"🌙\";\n  const cloud = \"☁️\";\n  const rain = \"🌧️\";\n  const thunder = \"⛈️\";\n  const snow = \"❄️\";\n  const fog = \"🌫️\";\n\n  if ([0, 1].includes(code)) return isDay ? sun : moon;\n  if ([2, 3].includes(code)) return cloud;\n  if ([51, 53, 55, 61, 63, 65, 80, 81, 82].includes(code)) return rain;\n  if ([71, 73, 75, 77, 85, 86].includes(code)) return snow;\n  if ([45, 48].includes(code)) return fog;\n  if ([95, 96, 99].includes(code)) return thunder;\n  return cloud;\n}\n\nexport function pressureToPreferred(unit, pressureMsl) {\n  // msl in hPa; imperial uses inHg\n  if (pressureMsl == null) return null;\n  return {\n    hpa: pressureMsl,\n    inhg: +(pressureMsl * 0.0295299830714).toFixed(2),\n  };\n}\n\nexport function aqiCategory(usAqi) {\n  if (usAqi == null) return { label: \"N/A\", color: \"gray\" };\n  if (usAqi <= 50) return { label: \"Good\", color: \"green\" };\n  if (usAqi <= 100) return { label: \"Moderate\", color: \"yellow\" };\n  if (usAqi <= 150) return { label: \"Unhealthy (SG)\", color: \"orange\" };\n  if (usAqi <= 200) return { label: \"Unhealthy\", color: \"red\" };\n  if (usAqi <= 300) return { label: \"Very Unhealthy\", color: \"purple\" };\n  return { label: \"Hazardous\", color: \"maroon\" };\n}\n\nexport function persistLocal(key, value) {\n  try {\n    localStorage.setItem(key, JSON.stringify(value));\n  } catch {}\n}\n\nexport function readLocal(key, fallback = null) {\n  try {\n    const v = localStorage.getItem(key);\n    return v ? JSON.parse(v) : fallback;\n  } catch {\n    return fallback;\n  }\n}\n\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,wDAAwD;;;;;;;;;;;;;;;AAExD,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEV,eAAe,YAAY,KAAK,EAAE,EAAE,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;IACzD,IAAI,CAAC,OAAO,OAAO,EAAE;IACrB,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;IAC7B,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS,OAAO;IACrC,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;IACjC,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU;IAE/B,MAAM,MAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;QAAE,OAAO;IAAW;IAC5D,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;IAC7B,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,OAAO,MAAM,WAAW,EAAE;AAC5B;AAEO,eAAe,cAAc,MAAM,EAAE,OAAO;IACjD,IAAI,CAAC,QAAQ,OAAO,EAAE;IACtB,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe;IACpC,IAAI,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC,WAAW;IAC7C,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS;IAC9B,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;IACjC,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU;IAC/B,MAAM,MAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;QAAE,OAAO;IAAW;IAC5D,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;IAC7B,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,OAAO,MAAM,WAAW,EAAE;AAC5B;AAEO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,WAAW,SAAS,EACpB,WAAW,KAAK,EAChB,QAAQ,EAAE,EACX;IACC,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY,OAAO;IACxC,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa,OAAO;IACzC,IAAI,YAAY,CAAC,GAAG,CAClB,WACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAET,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU;QAAC;QAAkB;KAAe,CAAC,IAAI,CAAC;IACvE,IAAI,YAAY,CAAC,GAAG,CAClB,SACA;QACE;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAET,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;IACjC,IAAI,YAAY,CAAC,GAAG,CAAC,iBAAiB;IACtC,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,gBAAgB,OAAO;IAC5C,IAAI,YAAY,CAAC,GAAG,CAAC,oBAAoB;IACzC,IAAI,YAAY,CAAC,GAAG,CAAC,kBAAkB;IACvC,OAAO,IAAI,QAAQ;AACrB;AAEO,eAAe,aAAa,IAAI;IACrC,MAAM,MAAM,gBAAgB;IAC5B,MAAM,MAAM,MAAM,MAAM,KAAK;QAAE,OAAO;IAAW;IACjD,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;IAC7B,OAAO,IAAI,IAAI;AACjB;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE;IACjD,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY,OAAO;IACxC,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa,OAAO;IACzC,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU;QAAC;QAAQ;QAAS;KAAS,CAAC,IAAI,CAAC;IAChE,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;IACjC,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,kBAAkB;IACvC,OAAO,IAAI,QAAQ;AACrB;AAEO,eAAe,SAAS,IAAI;IACjC,MAAM,MAAM,MAAM,MAAM,YAAY,OAAO;QAAE,OAAO;IAAW;IAC/D,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;IAC7B,OAAO,IAAI,IAAI;AACjB;AAEO,SAAS,kBAAkB,IAAI;IACpC,MAAM,MAAM;QACV,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,OAAO,GAAG,CAAC,KAAK,IAAI;AACtB;AAEO,SAAS,mBAAmB,IAAI,EAAE,QAAQ,CAAC;IAChD,MAAM,MAAM;IACZ,MAAM,OAAO;IACb,MAAM,QAAQ;IACd,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO;IACb,MAAM,MAAM;IAEZ,IAAI;QAAC;QAAG;KAAE,CAAC,QAAQ,CAAC,OAAO,OAAO,QAAQ,MAAM;IAChD,IAAI;QAAC;QAAG;KAAE,CAAC,QAAQ,CAAC,OAAO,OAAO;IAClC,IAAI;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG,CAAC,QAAQ,CAAC,OAAO,OAAO;IAChE,IAAI;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG,CAAC,QAAQ,CAAC,OAAO,OAAO;IACpD,IAAI;QAAC;QAAI;KAAG,CAAC,QAAQ,CAAC,OAAO,OAAO;IACpC,IAAI;QAAC;QAAI;QAAI;KAAG,CAAC,QAAQ,CAAC,OAAO,OAAO;IACxC,OAAO;AACT;AAEO,SAAS,oBAAoB,IAAI,EAAE,WAAW;IACnD,iCAAiC;IACjC,IAAI,eAAe,MAAM,OAAO;IAChC,OAAO;QACL,KAAK;QACL,MAAM,CAAC,CAAC,cAAc,eAAe,EAAE,OAAO,CAAC;IACjD;AACF;AAEO,SAAS,YAAY,KAAK;IAC/B,IAAI,SAAS,MAAM,OAAO;QAAE,OAAO;QAAO,OAAO;IAAO;IACxD,IAAI,SAAS,IAAI,OAAO;QAAE,OAAO;QAAQ,OAAO;IAAQ;IACxD,IAAI,SAAS,KAAK,OAAO;QAAE,OAAO;QAAY,OAAO;IAAS;IAC9D,IAAI,SAAS,KAAK,OAAO;QAAE,OAAO;QAAkB,OAAO;IAAS;IACpE,IAAI,SAAS,KAAK,OAAO;QAAE,OAAO;QAAa,OAAO;IAAM;IAC5D,IAAI,SAAS,KAAK,OAAO;QAAE,OAAO;QAAkB,OAAO;IAAS;IACpE,OAAO;QAAE,OAAO;QAAa,OAAO;IAAS;AAC/C;AAEO,SAAS,aAAa,GAAG,EAAE,KAAK;IACrC,IAAI;QACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;IAC3C,EAAE,OAAM,CAAC;AACX;AAEO,SAAS,UAAU,GAAG,EAAE,WAAW,IAAI;IAC5C,IAAI;QACF,MAAM,IAAI,aAAa,OAAO,CAAC;QAC/B,OAAO,IAAI,KAAK,KAAK,CAAC,KAAK;IAC7B,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/weather-app/app/page.js"], "sourcesContent": ["\"use client\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { Box, Card, Container, Flex, Grid, Heading, IconButton, Inset, Kbd, SegmentedControl, Separator, Spinner, Text, TextField, Tooltip } from \"@radix-ui/themes\";\nimport { Cross2Icon, MagnifyingGlassIcon, TargetIcon, HeartIcon } from \"@radix-ui/react-icons\";\nimport {\n  geocodeCity,\n  geocodePostal,\n  fetchWeather,\n  fetchAQI,\n  weatherCodeToText,\n  weatherCodeToEmoji,\n  pressureToPreferred,\n  aqiCategory,\n  persistLocal,\n  readLocal,\n} from \"@/lib/openmeteo\";\n\nexport default function Home() {\n  const [query, setQuery] = useState(\"\");\n  const [results, setResults] = useState([]);\n  const [selected, setSelected] = useState(readLocal(\"sel\", null));\n  const [units, setUnits] = useState(readLocal(\"units\", { temp: \"celsius\", wind: \"kmh\" }));\n  const [data, setData] = useState(null);\n  const [aqi, setAqi] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [favorites, setFavorites] = useState(readLocal(\"favs\", []));\n\n  useEffect(() => persistLocal(\"units\", units), [units]);\n  useEffect(() => persistLocal(\"sel\", selected), [selected]);\n  useEffect(() => persistLocal(\"favs\", favorites), [favorites]);\n\n  useEffect(() => {\n    const controller = new AbortController();\n    async function run() {\n      if (!selected) return;\n      setLoading(true);\n      try {\n        const w = await fetchWeather({\n          latitude: selected.latitude,\n          longitude: selected.longitude,\n          tempUnit: units.temp,\n          windUnit: units.wind,\n        });\n        setData(w);\n        try {\n          const a = await fetchAQI({ latitude: selected.latitude, longitude: selected.longitude });\n          setAqi(a);\n        } catch {}\n      } catch (e) {\n        console.error(e);\n      } finally {\n        setLoading(false);\n      }\n    }\n    run();\n    return () => controller.abort();\n  }, [selected, units]);\n\n  async function onSearch(text) {\n    setQuery(text);\n    if (!text || text.length < 2) {\n      setResults([]);\n      return;\n    }\n    const isZip = /^\\d{3,}$/.test(text.trim());\n    try {\n      const items = isZip ? await geocodePostal(text.trim()) : await geocodeCity(text.trim());\n      setResults(items);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  function choosePlace(place) {\n    setSelected(place);\n    setResults([]);\n    setQuery(`${place.name}${place.admin1 ? \", \" + place.admin1 : \"\"}${place.country ? \", \" + place.country : \"\"}`);\n  }\n\n  function useGPS() {\n    if (!navigator.geolocation) return;\n    navigator.geolocation.getCurrentPosition(async (pos) => {\n      const { latitude, longitude } = pos.coords;\n      const place = { name: \"Current location\", latitude, longitude };\n      choosePlace(place);\n    });\n  }\n\n  function toggleFavorite() {\n    if (!selected) return;\n    const key = `${selected.name}-${selected.latitude}-${selected.longitude}`;\n    const exists = favorites.find((f) => `${f.name}-${f.latitude}-${f.longitude}` === key);\n    const next = exists ? favorites.filter((f) => `${f.name}-${f.latitude}-${f.longitude}` !== key) : [selected, ...favorites].slice(0, 8);\n    setFavorites(next);\n  }\n\n  const current = data?.current;\n  const daily = data?.daily;\n  const hourly = data?.hourly;\n  const pressure = pressureToPreferred(\"auto\", current?.pressure_msl);\n  const isFav = selected && favorites.some((f) => f.latitude === selected.latitude && f.longitude === selected.longitude && f.name === selected.name);\n\n  return (\n    <Container size=\"3\">\n      <Box py=\"4\">\n        <Heading size=\"6\">Weather</Heading>\n        <Text color=\"gray\">Powered by Open-Meteo</Text>\n      </Box>\n\n      <Card size=\"3\">\n        <Flex align=\"center\" gap=\"2\" wrap=\"wrap\">\n          <TextField.Root size=\"3\" style={{ flex: 1 }} value={query} onChange={(e) => onSearch(e.target.value)} placeholder=\"Search by city or ZIP\">\n            <TextField.Slot>\n              <MagnifyingGlassIcon />\n            </TextField.Slot>\n            {query && (\n              <TextField.Slot side=\"right\">\n                <IconButton variant=\"ghost\" color=\"gray\" onClick={() => { setQuery(\"\"); setResults([]); }}>\n                  <Cross2Icon />\n                </IconButton>\n              </TextField.Slot>\n            )}\n          </TextField.Root>\n          <Tooltip content=\"Use current location\">\n            <IconButton onClick={useGPS} variant=\"soft\">\n              <TargetIcon />\n            </IconButton>\n          </Tooltip>\n          <Tooltip content={isFav ? \"Remove favorite\" : \"Save favorite\"}>\n            <IconButton onClick={toggleFavorite} variant={isFav ? \"solid\" : \"soft\"} color={isFav ? \"red\" : undefined}>\n              <HeartIcon />\n            </IconButton>\n          </Tooltip>\n        </Flex>\n\n        {results.length > 0 && (\n          <Box mt=\"2\">\n            <Separator size=\"4\" />\n            <Grid columns={{ initial: \"1\", sm: \"2\" }} gap=\"2\" mt=\"2\">\n              {results.map((r) => (\n                <Card key={`${r.id}-${r.name}`} onClick={() => choosePlace(r)} style={{ cursor: \"pointer\" }}>\n                  <Text weight=\"medium\">{r.name}</Text>\n                  <Text color=\"gray\" size=\"2\">{[r.admin1, r.country].filter(Boolean).join(\", \")}</Text>\n                </Card>\n              ))}\n            </Grid>\n          </Box>\n        )}\n      </Card>\n\n      {favorites.length > 0 && (\n        <Box mt=\"3\">\n          <Text weight=\"medium\">Favorites</Text>\n          <Flex mt=\"2\" gap=\"2\" wrap=\"wrap\">\n            {favorites.map((f, i) => (\n              <Card key={`${f.name}-${i}`} style={{ cursor: \"pointer\" }} onClick={() => choosePlace(f)}>\n                <Text>{f.name}</Text>\n                <Text color=\"gray\" size=\"2\">{f.country || \"\"}</Text>\n              </Card>\n            ))}\n          </Flex>\n        </Box>\n      )}\n\n      <Box mt=\"4\">\n        <Flex align=\"center\" justify=\"between\" wrap=\"wrap\" gap=\"3\">\n          <Text size=\"2\" color=\"gray\">Units</Text>\n          <Flex align=\"center\" gap=\"3\">\n            <SegmentedControl.Root value={units.temp} onValueChange={(v) => setUnits((u) => ({ ...u, temp: v }))}>\n              <SegmentedControl.Item value=\"celsius\">°C</SegmentedControl.Item>\n              <SegmentedControl.Item value=\"fahrenheit\">°F</SegmentedControl.Item>\n            </SegmentedControl.Root>\n            <SegmentedControl.Root value={units.wind} onValueChange={(v) => setUnits((u) => ({ ...u, wind: v }))}>\n              <SegmentedControl.Item value=\"kmh\">km/h</SegmentedControl.Item>\n              <SegmentedControl.Item value=\"mph\">mph</SegmentedControl.Item>\n            </SegmentedControl.Root>\n          </Flex>\n        </Flex>\n      </Box>\n\n      <Box mt=\"4\">\n        {!selected && <Text color=\"gray\">Search for a location to see weather. Tip: use your GPS with the target icon.</Text>}\n        {loading && (\n          <Flex align=\"center\" gap=\"2\"><Spinner /> <Text>Loading…</Text></Flex>\n        )}\n\n        {selected && current && (\n          <Card>\n            <Flex direction=\"column\" gap=\"3\">\n              <Flex align=\"center\" justify=\"between\" wrap=\"wrap\" gap=\"2\">\n                <Heading size=\"5\">{selected.name}</Heading>\n                <Text color=\"gray\">{[selected.admin1, selected.country].filter(Boolean).join(\", \")}</Text>\n              </Flex>\n              <Flex align=\"center\" gap=\"4\" wrap=\"wrap\">\n                <Text size=\"9\">{weatherCodeToEmoji(current.weather_code, current.is_day)}</Text>\n                <Heading size=\"9\">{Math.round(current.temperature_2m)}°{units.temp === \"celsius\" ? \"C\" : \"F\"}</Heading>\n                <Text color=\"gray\">{weatherCodeToText(current.weather_code)}</Text>\n              </Flex>\n              <Grid columns={{ initial: \"2\", sm: \"4\" }} gap=\"3\">\n                <Card><Inset><Text size=\"2\" color=\"gray\">Feels like</Text><Heading size=\"6\">{Math.round(current.apparent_temperature)}°</Heading></Inset></Card>\n                <Card><Inset><Text size=\"2\" color=\"gray\">Humidity</Text><Heading size=\"6\">{current.relative_humidity_2m}%</Heading></Inset></Card>\n                <Card><Inset><Text size=\"2\" color=\"gray\">Wind</Text><Heading size=\"6\">{Math.round(current.wind_speed_10m)} {units.wind}</Heading></Inset></Card>\n                <Card><Inset><Text size=\"2\" color=\"gray\">Pressure</Text><Heading size=\"6\">{units.temp === \"celsius\" ? `${pressure.hpa} hPa` : `${pressure.inhg} inHg`}</Heading></Inset></Card>\n              </Grid>\n\n              {daily && daily.time && (\n                <Box>\n                  <Text weight=\"medium\">7-Day Forecast</Text>\n                  <Grid columns={{ initial: \"2\", sm: \"4\", md: \"7\" }} gap=\"2\" mt=\"2\">\n                    {daily.time.map((t, i) => (\n                      <Card key={t}>\n                        <Flex direction=\"column\" align=\"center\" gap=\"1\">\n                          <Text size=\"2\" color=\"gray\">{new Date(t).toLocaleDateString(undefined, { weekday: \"short\" })}</Text>\n                          <Text size=\"6\">{weatherCodeToEmoji(daily.weather_code[i], 1)}</Text>\n                          <Text size=\"2\">{Math.round(daily.temperature_2m_max[i])}° / {Math.round(daily.temperature_2m_min[i])}°</Text>\n                        </Flex>\n                      </Card>\n                    ))}\n                  </Grid>\n                </Box>\n              )}\n\n              {hourly && hourly.time && (\n                <Box>\n                  <Text weight=\"medium\">Next hours</Text>\n                  <Flex mt=\"2\" gap=\"2\" wrap=\"wrap\">\n                    {hourly.time.slice(0, 12).map((t, i) => (\n                      <Card key={t}>\n                        <Flex direction=\"column\" align=\"center\" gap=\"1\">\n                          <Text size=\"2\" color=\"gray\">{new Date(t).toLocaleTimeString(undefined, { hour: \"2-digit\", minute: \"2-digit\" })}</Text>\n                          <Text size=\"5\">{weatherCodeToEmoji(hourly.weather_code[i], 1)}</Text>\n                          <Text>{Math.round(hourly.temperature_2m[i])}°</Text>\n                        </Flex>\n                      </Card>\n                    ))}\n                  </Flex>\n                </Box>\n              )}\n\n              {aqi?.hourly?.us_aqi && (\n                <Box>\n                  <Text weight=\"medium\">Air Quality</Text>\n                  <Flex mt=\"2\" align=\"center\" gap=\"2\">\n                    {(() => {\n                      const v = aqi.hourly.us_aqi[0];\n                      const cat = aqiCategory(v);\n                      return (\n                        <Card>\n                          <Flex align=\"center\" gap=\"2\" p=\"2\">\n                            <Text>US AQI: {v}</Text>\n                            <Text color=\"gray\">{cat.label}</Text>\n                          </Flex>\n                        </Card>\n                      );\n                    })()}\n                  </Flex>\n                </Box>\n              )}\n            </Flex>\n          </Card>\n        )}\n      </Box>\n\n      <Box my=\"5\">\n        <Separator size=\"4\" />\n        <Text size=\"1\" color=\"gray\">Shortcuts: <Kbd>Ctrl</Kbd> + <Kbd>K</Kbd> to focus search</Text>\n      </Box>\n    </Container>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD,EAAE,OAAO;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QAAE,MAAM;QAAW,MAAM;IAAM;IACrF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,EAAE;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,IAAM,CAAA,GAAA,gHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,QAAQ;QAAC;KAAM;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,IAAM,CAAA,GAAA,gHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,WAAW;QAAC;KAAS;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,IAAM,CAAA,GAAA,gHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,YAAY;QAAC;KAAU;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,IAAI;QACvB,eAAe;YACb,IAAI,CAAC,UAAU;YACf,WAAW;YACX,IAAI;gBACF,MAAM,IAAI,MAAM,CAAA,GAAA,gHAAA,CAAA,eAAY,AAAD,EAAE;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,WAAW,SAAS,SAAS;oBAC7B,UAAU,MAAM,IAAI;oBACpB,UAAU,MAAM,IAAI;gBACtB;gBACA,QAAQ;gBACR,IAAI;oBACF,MAAM,IAAI,MAAM,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;wBAAE,UAAU,SAAS,QAAQ;wBAAE,WAAW,SAAS,SAAS;oBAAC;oBACtF,OAAO;gBACT,EAAE,OAAM,CAAC;YACX,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC;YAChB,SAAU;gBACR,WAAW;YACb;QACF;QACA;QACA,OAAO,IAAM,WAAW,KAAK;IAC/B,GAAG;QAAC;QAAU;KAAM;IAEpB,eAAe,SAAS,IAAI;QAC1B,SAAS;QACT,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC5B,WAAW,EAAE;YACb;QACF;QACA,MAAM,QAAQ,WAAW,IAAI,CAAC,KAAK,IAAI;QACvC,IAAI;YACF,MAAM,QAAQ,QAAQ,MAAM,CAAA,GAAA,gHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI,MAAM,MAAM,CAAA,GAAA,gHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI;YACpF,WAAW;QACb,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,SAAS,YAAY,KAAK;QACxB,YAAY;QACZ,WAAW,EAAE;QACb,SAAS,GAAG,MAAM,IAAI,GAAG,MAAM,MAAM,GAAG,OAAO,MAAM,MAAM,GAAG,KAAK,MAAM,OAAO,GAAG,OAAO,MAAM,OAAO,GAAG,IAAI;IAChH;IAEA,SAAS;QACP,IAAI,CAAC,UAAU,WAAW,EAAE;QAC5B,UAAU,WAAW,CAAC,kBAAkB,CAAC,OAAO;YAC9C,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,MAAM;YAC1C,MAAM,QAAQ;gBAAE,MAAM;gBAAoB;gBAAU;YAAU;YAC9D,YAAY;QACd;IACF;IAEA,SAAS;QACP,IAAI,CAAC,UAAU;QACf,MAAM,MAAM,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,QAAQ,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE;QACzE,MAAM,SAAS,UAAU,IAAI,CAAC,CAAC,IAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK;QAClF,MAAM,OAAO,SAAS,UAAU,MAAM,CAAC,CAAC,IAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,OAAO;YAAC;eAAa;SAAU,CAAC,KAAK,CAAC,GAAG;QACpI,aAAa;IACf;IAEA,MAAM,UAAU,MAAM;IACtB,MAAM,QAAQ,MAAM;IACpB,MAAM,SAAS,MAAM;IACrB,MAAM,WAAW,CAAA,GAAA,gHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,SAAS;IACtD,MAAM,QAAQ,YAAY,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,SAAS,QAAQ,IAAI,EAAE,SAAS,KAAK,SAAS,SAAS,IAAI,EAAE,IAAI,KAAK,SAAS,IAAI;IAElJ,qBACE,8OAAC,iLAAA,CAAA,YAAS;QAAC,MAAK;;0BACd,8OAAC,2KAAA,CAAA,MAAG;gBAAC,IAAG;;kCACN,8OAAC,+KAAA,CAAA,UAAO;wBAAC,MAAK;kCAAI;;;;;;kCAClB,8OAAC,4KAAA,CAAA,OAAI;wBAAC,OAAM;kCAAO;;;;;;;;;;;;0BAGrB,8OAAC,4KAAA,CAAA,OAAI;gBAAC,MAAK;;kCACT,8OAAC,4KAAA,CAAA,OAAI;wBAAC,OAAM;wBAAS,KAAI;wBAAI,MAAK;;0CAChC,8OAAC,0NAAA,CAAA,YAAS,CAAC,IAAI;gCAAC,MAAK;gCAAI,OAAO;oCAAE,MAAM;gCAAE;gCAAG,OAAO;gCAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCAAG,aAAY;;kDAChH,8OAAC,0NAAA,CAAA,YAAS,CAAC,IAAI;kDACb,cAAA,8OAAC,gLAAA,CAAA,sBAAmB;;;;;;;;;;oCAErB,uBACC,8OAAC,0NAAA,CAAA,YAAS,CAAC,IAAI;wCAAC,MAAK;kDACnB,cAAA,8OAAC,sLAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAQ,OAAM;4CAAO,SAAS;gDAAQ,SAAS;gDAAK,WAAW,EAAE;4CAAG;sDACtF,cAAA,8OAAC,gLAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;0CAKnB,8OAAC,+KAAA,CAAA,UAAO;gCAAC,SAAQ;0CACf,cAAA,8OAAC,sLAAA,CAAA,aAAU;oCAAC,SAAS;oCAAQ,SAAQ;8CACnC,cAAA,8OAAC,gLAAA,CAAA,aAAU;;;;;;;;;;;;;;;0CAGf,8OAAC,+KAAA,CAAA,UAAO;gCAAC,SAAS,QAAQ,oBAAoB;0CAC5C,cAAA,8OAAC,sLAAA,CAAA,aAAU;oCAAC,SAAS;oCAAgB,SAAS,QAAQ,UAAU;oCAAQ,OAAO,QAAQ,QAAQ;8CAC7F,cAAA,8OAAC,gLAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;;;;;oBAKf,QAAQ,MAAM,GAAG,mBAChB,8OAAC,2KAAA,CAAA,MAAG;wBAAC,IAAG;;0CACN,8OAAC,iLAAA,CAAA,YAAS;gCAAC,MAAK;;;;;;0CAChB,8OAAC,4KAAA,CAAA,OAAI;gCAAC,SAAS;oCAAE,SAAS;oCAAK,IAAI;gCAAI;gCAAG,KAAI;gCAAI,IAAG;0CAClD,QAAQ,GAAG,CAAC,CAAC,kBACZ,8OAAC,4KAAA,CAAA,OAAI;wCAA2B,SAAS,IAAM,YAAY;wCAAI,OAAO;4CAAE,QAAQ;wCAAU;;0DACxF,8OAAC,4KAAA,CAAA,OAAI;gDAAC,QAAO;0DAAU,EAAE,IAAI;;;;;;0DAC7B,8OAAC,4KAAA,CAAA,OAAI;gDAAC,OAAM;gDAAO,MAAK;0DAAK;oDAAC,EAAE,MAAM;oDAAE,EAAE,OAAO;iDAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;;;;;;;uCAF/D,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;YAUvC,UAAU,MAAM,GAAG,mBAClB,8OAAC,2KAAA,CAAA,MAAG;gBAAC,IAAG;;kCACN,8OAAC,4KAAA,CAAA,OAAI;wBAAC,QAAO;kCAAS;;;;;;kCACtB,8OAAC,4KAAA,CAAA,OAAI;wBAAC,IAAG;wBAAI,KAAI;wBAAI,MAAK;kCACvB,UAAU,GAAG,CAAC,CAAC,GAAG,kBACjB,8OAAC,4KAAA,CAAA,OAAI;gCAAwB,OAAO;oCAAE,QAAQ;gCAAU;gCAAG,SAAS,IAAM,YAAY;;kDACpF,8OAAC,4KAAA,CAAA,OAAI;kDAAE,EAAE,IAAI;;;;;;kDACb,8OAAC,4KAAA,CAAA,OAAI;wCAAC,OAAM;wCAAO,MAAK;kDAAK,EAAE,OAAO,IAAI;;;;;;;+BAFjC,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG;;;;;;;;;;;;;;;;0BASnC,8OAAC,2KAAA,CAAA,MAAG;gBAAC,IAAG;0BACN,cAAA,8OAAC,4KAAA,CAAA,OAAI;oBAAC,OAAM;oBAAS,SAAQ;oBAAU,MAAK;oBAAO,KAAI;;sCACrD,8OAAC,4KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,OAAM;sCAAO;;;;;;sCAC5B,8OAAC,4KAAA,CAAA,OAAI;4BAAC,OAAM;4BAAS,KAAI;;8CACvB,8OAAC,wOAAA,CAAA,mBAAgB,CAAC,IAAI;oCAAC,OAAO,MAAM,IAAI;oCAAE,eAAe,CAAC,IAAM,SAAS,CAAC,IAAM,CAAC;gDAAE,GAAG,CAAC;gDAAE,MAAM;4CAAE,CAAC;;sDAChG,8OAAC,wOAAA,CAAA,mBAAgB,CAAC,IAAI;4CAAC,OAAM;sDAAU;;;;;;sDACvC,8OAAC,wOAAA,CAAA,mBAAgB,CAAC,IAAI;4CAAC,OAAM;sDAAa;;;;;;;;;;;;8CAE5C,8OAAC,wOAAA,CAAA,mBAAgB,CAAC,IAAI;oCAAC,OAAO,MAAM,IAAI;oCAAE,eAAe,CAAC,IAAM,SAAS,CAAC,IAAM,CAAC;gDAAE,GAAG,CAAC;gDAAE,MAAM;4CAAE,CAAC;;sDAChG,8OAAC,wOAAA,CAAA,mBAAgB,CAAC,IAAI;4CAAC,OAAM;sDAAM;;;;;;sDACnC,8OAAC,wOAAA,CAAA,mBAAgB,CAAC,IAAI;4CAAC,OAAM;sDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC,2KAAA,CAAA,MAAG;gBAAC,IAAG;;oBACL,CAAC,0BAAY,8OAAC,4KAAA,CAAA,OAAI;wBAAC,OAAM;kCAAO;;;;;;oBAChC,yBACC,8OAAC,4KAAA,CAAA,OAAI;wBAAC,OAAM;wBAAS,KAAI;;0CAAI,8OAAC,+KAAA,CAAA,UAAO;;;;;4BAAG;0CAAC,8OAAC,4KAAA,CAAA,OAAI;0CAAC;;;;;;;;;;;;oBAGhD,YAAY,yBACX,8OAAC,4KAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,4KAAA,CAAA,OAAI;4BAAC,WAAU;4BAAS,KAAI;;8CAC3B,8OAAC,4KAAA,CAAA,OAAI;oCAAC,OAAM;oCAAS,SAAQ;oCAAU,MAAK;oCAAO,KAAI;;sDACrD,8OAAC,+KAAA,CAAA,UAAO;4CAAC,MAAK;sDAAK,SAAS,IAAI;;;;;;sDAChC,8OAAC,4KAAA,CAAA,OAAI;4CAAC,OAAM;sDAAQ;gDAAC,SAAS,MAAM;gDAAE,SAAS,OAAO;6CAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;;;;;;;;;;;;8CAE/E,8OAAC,4KAAA,CAAA,OAAI;oCAAC,OAAM;oCAAS,KAAI;oCAAI,MAAK;;sDAChC,8OAAC,4KAAA,CAAA,OAAI;4CAAC,MAAK;sDAAK,CAAA,GAAA,gHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,YAAY,EAAE,QAAQ,MAAM;;;;;;sDACvE,8OAAC,+KAAA,CAAA,UAAO;4CAAC,MAAK;;gDAAK,KAAK,KAAK,CAAC,QAAQ,cAAc;gDAAE;gDAAE,MAAM,IAAI,KAAK,YAAY,MAAM;;;;;;;sDACzF,8OAAC,4KAAA,CAAA,OAAI;4CAAC,OAAM;sDAAQ,CAAA,GAAA,gHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,YAAY;;;;;;;;;;;;8CAE5D,8OAAC,4KAAA,CAAA,OAAI;oCAAC,SAAS;wCAAE,SAAS;wCAAK,IAAI;oCAAI;oCAAG,KAAI;;sDAC5C,8OAAC,4KAAA,CAAA,OAAI;sDAAC,cAAA,8OAAC,6KAAA,CAAA,QAAK;;kEAAC,8OAAC,4KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,OAAM;kEAAO;;;;;;kEAAiB,8OAAC,+KAAA,CAAA,UAAO;wDAAC,MAAK;;4DAAK,KAAK,KAAK,CAAC,QAAQ,oBAAoB;4DAAE;;;;;;;;;;;;;;;;;;sDACtH,8OAAC,4KAAA,CAAA,OAAI;sDAAC,cAAA,8OAAC,6KAAA,CAAA,QAAK;;kEAAC,8OAAC,4KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,OAAM;kEAAO;;;;;;kEAAe,8OAAC,+KAAA,CAAA,UAAO;wDAAC,MAAK;;4DAAK,QAAQ,oBAAoB;4DAAC;;;;;;;;;;;;;;;;;;sDACxG,8OAAC,4KAAA,CAAA,OAAI;sDAAC,cAAA,8OAAC,6KAAA,CAAA,QAAK;;kEAAC,8OAAC,4KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,OAAM;kEAAO;;;;;;kEAAW,8OAAC,+KAAA,CAAA,UAAO;wDAAC,MAAK;;4DAAK,KAAK,KAAK,CAAC,QAAQ,cAAc;4DAAE;4DAAE,MAAM,IAAI;;;;;;;;;;;;;;;;;;sDACtH,8OAAC,4KAAA,CAAA,OAAI;sDAAC,cAAA,8OAAC,6KAAA,CAAA,QAAK;;kEAAC,8OAAC,4KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,OAAM;kEAAO;;;;;;kEAAe,8OAAC,+KAAA,CAAA,UAAO;wDAAC,MAAK;kEAAK,MAAM,IAAI,KAAK,YAAY,GAAG,SAAS,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;gCAGtJ,SAAS,MAAM,IAAI,kBAClB,8OAAC,2KAAA,CAAA,MAAG;;sDACF,8OAAC,4KAAA,CAAA,OAAI;4CAAC,QAAO;sDAAS;;;;;;sDACtB,8OAAC,4KAAA,CAAA,OAAI;4CAAC,SAAS;gDAAE,SAAS;gDAAK,IAAI;gDAAK,IAAI;4CAAI;4CAAG,KAAI;4CAAI,IAAG;sDAC3D,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBAClB,8OAAC,4KAAA,CAAA,OAAI;8DACH,cAAA,8OAAC,4KAAA,CAAA,OAAI;wDAAC,WAAU;wDAAS,OAAM;wDAAS,KAAI;;0EAC1C,8OAAC,4KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,OAAM;0EAAQ,IAAI,KAAK,GAAG,kBAAkB,CAAC,WAAW;oEAAE,SAAS;gEAAQ;;;;;;0EAC1F,8OAAC,4KAAA,CAAA,OAAI;gEAAC,MAAK;0EAAK,CAAA,GAAA,gHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE;;;;;;0EAC1D,8OAAC,4KAAA,CAAA,OAAI;gEAAC,MAAK;;oEAAK,KAAK,KAAK,CAAC,MAAM,kBAAkB,CAAC,EAAE;oEAAE;oEAAK,KAAK,KAAK,CAAC,MAAM,kBAAkB,CAAC,EAAE;oEAAE;;;;;;;;;;;;;mDAJ9F;;;;;;;;;;;;;;;;gCAYlB,UAAU,OAAO,IAAI,kBACpB,8OAAC,2KAAA,CAAA,MAAG;;sDACF,8OAAC,4KAAA,CAAA,OAAI;4CAAC,QAAO;sDAAS;;;;;;sDACtB,8OAAC,4KAAA,CAAA,OAAI;4CAAC,IAAG;4CAAI,KAAI;4CAAI,MAAK;sDACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC,4KAAA,CAAA,OAAI;8DACH,cAAA,8OAAC,4KAAA,CAAA,OAAI;wDAAC,WAAU;wDAAS,OAAM;wDAAS,KAAI;;0EAC1C,8OAAC,4KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,OAAM;0EAAQ,IAAI,KAAK,GAAG,kBAAkB,CAAC,WAAW;oEAAE,MAAM;oEAAW,QAAQ;gEAAU;;;;;;0EAC5G,8OAAC,4KAAA,CAAA,OAAI;gEAAC,MAAK;0EAAK,CAAA,GAAA,gHAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,YAAY,CAAC,EAAE,EAAE;;;;;;0EAC3D,8OAAC,4KAAA,CAAA,OAAI;;oEAAE,KAAK,KAAK,CAAC,OAAO,cAAc,CAAC,EAAE;oEAAE;;;;;;;;;;;;;mDAJrC;;;;;;;;;;;;;;;;gCAYlB,KAAK,QAAQ,wBACZ,8OAAC,2KAAA,CAAA,MAAG;;sDACF,8OAAC,4KAAA,CAAA,OAAI;4CAAC,QAAO;sDAAS;;;;;;sDACtB,8OAAC,4KAAA,CAAA,OAAI;4CAAC,IAAG;4CAAI,OAAM;4CAAS,KAAI;sDAC7B,CAAC;gDACA,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE;gDAC9B,MAAM,MAAM,CAAA,GAAA,gHAAA,CAAA,cAAW,AAAD,EAAE;gDACxB,qBACE,8OAAC,4KAAA,CAAA,OAAI;8DACH,cAAA,8OAAC,4KAAA,CAAA,OAAI;wDAAC,OAAM;wDAAS,KAAI;wDAAI,GAAE;;0EAC7B,8OAAC,4KAAA,CAAA,OAAI;;oEAAC;oEAAS;;;;;;;0EACf,8OAAC,4KAAA,CAAA,OAAI;gEAAC,OAAM;0EAAQ,IAAI,KAAK;;;;;;;;;;;;;;;;;4CAIrC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASf,8OAAC,2KAAA,CAAA,MAAG;gBAAC,IAAG;;kCACN,8OAAC,iLAAA,CAAA,YAAS;wBAAC,MAAK;;;;;;kCAChB,8OAAC,4KAAA,CAAA,OAAI;wBAAC,MAAK;wBAAI,OAAM;;4BAAO;0CAAW,8OAAC,2KAAA,CAAA,MAAG;0CAAC;;;;;;4BAAU;0CAAG,8OAAC,2KAAA,CAAA,MAAG;0CAAC;;;;;;4BAAO;;;;;;;;;;;;;;;;;;;AAI7E", "debugId": null}}]}