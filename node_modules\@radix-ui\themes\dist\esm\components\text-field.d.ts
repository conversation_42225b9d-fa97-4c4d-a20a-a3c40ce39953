import * as React from 'react';
import { textFieldRootPropDefs, textFieldSlotPropDefs } from './text-field.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { NotInputTextualAttributes } from '../helpers/input-attributes.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type TextFieldRootOwnProps = GetPropDefTypes<typeof textFieldRootPropDefs> & {
    defaultValue?: string | number;
    value?: string | number;
    type?: 'date' | 'datetime-local' | 'email' | 'hidden' | 'month' | 'number' | 'password' | 'search' | 'tel' | 'text' | 'time' | 'url' | 'week';
};
type TextFieldInputProps = ComponentPropsWithout<'input', NotInputTextualAttributes | 'color' | 'defaultValue' | 'size' | 'type' | 'value'>;
interface TextFieldRootProps extends TextFieldInputProps, MarginProps, TextFieldRootOwnProps {
}
declare const TextFieldRoot: React.ForwardRefExoticComponent<TextFieldRootProps & React.RefAttributes<HTMLInputElement>>;
type TextFieldSlotOwnProps = GetPropDefTypes<typeof textFieldSlotPropDefs>;
interface TextFieldSlotProps extends ComponentPropsWithout<'div', RemovedProps>, TextFieldSlotOwnProps {
}
declare const TextFieldSlot: React.ForwardRefExoticComponent<TextFieldSlotProps & React.RefAttributes<HTMLDivElement>>;
export { TextFieldRoot as Root, TextFieldSlot as Slot };
export type { TextFieldRootProps as RootProps, TextFieldSlotProps as SlotProps };
//# sourceMappingURL=text-field.d.ts.map