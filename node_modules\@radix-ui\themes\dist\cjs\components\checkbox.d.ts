import * as React from 'react';
import { Checkbox as CheckboxPrimitive } from 'radix-ui';
import { checkboxPropDefs } from './checkbox.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type CheckboxOwnProps = GetPropDefTypes<typeof checkboxPropDefs>;
interface CheckboxProps extends ComponentPropsWithout<typeof CheckboxPrimitive.Root, 'asChild' | 'color' | 'defaultValue' | 'children'>, MarginProps, CheckboxOwnProps {
}
declare const Checkbox: React.ForwardRefExoticComponent<CheckboxProps & React.RefAttributes<HTMLButtonElement>>;
export { Checkbox };
export type { CheckboxProps };
//# sourceMappingURL=checkbox.d.ts.map