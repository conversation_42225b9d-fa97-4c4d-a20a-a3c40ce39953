{"version": 3, "sources": ["../../../src/components/switch.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Switch as SwitchPrimitive } from 'radix-ui';\n\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\nimport { switchPropDefs } from './switch.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\nimport type { ComponentPropsWithout } from '../helpers/component-props.js';\n\ntype SwitchElement = React.ElementRef<typeof SwitchPrimitive.Root>;\ntype SwitchOwnProps = GetPropDefTypes<typeof switchPropDefs>;\ninterface SwitchProps\n  extends ComponentPropsWithout<\n      typeof SwitchPrimitive.Root,\n      'asChild' | 'color' | 'defaultValue' | 'children'\n    >,\n    MarginProps,\n    SwitchOwnProps {}\nconst Switch = React.forwardRef<SwitchElement, SwitchProps>((props, forwardedRef) => {\n  const { className, color, radius, ...switchProps } = extractProps(\n    props,\n    switchPropDefs,\n    marginPropDefs\n  );\n  return (\n    <SwitchPrimitive.Root\n      data-accent-color={color}\n      data-radius={radius}\n      {...switchProps}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-reset', 'rt-SwitchRoot', className)}\n    >\n      <SwitchPrimitive.Thumb\n        className={classNames('rt-SwitchThumb', { 'rt-high-contrast': props.highContrast })}\n      />\n    </SwitchPrimitive.Root>\n  );\n});\nSwitch.displayName = 'Switch';\n\nexport { Switch };\nexport type { SwitchProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,UAAUC,MAAuB,WAE1C,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,kBAAAC,MAAsB,2BAC/B,OAAS,kBAAAC,MAAsB,oBAe/B,MAAMC,EAASN,EAAM,WAAuC,CAACO,EAAOC,IAAiB,CACnF,KAAM,CAAE,UAAAC,EAAW,MAAAC,EAAO,OAAAC,EAAQ,GAAGC,CAAY,EAAIT,EACnDI,EACAF,EACAD,CACF,EACA,OACEJ,EAAA,cAACE,EAAgB,KAAhB,CACC,oBAAmBQ,EACnB,cAAaC,EACZ,GAAGC,EACJ,QAAS,GACT,IAAKJ,EACL,UAAWP,EAAW,WAAY,gBAAiBQ,CAAS,GAE5DT,EAAA,cAACE,EAAgB,MAAhB,CACC,UAAWD,EAAW,iBAAkB,CAAE,mBAAoBM,EAAM,YAAa,CAAC,EACpF,CACF,CAEJ,CAAC,EACDD,EAAO,YAAc", "names": ["React", "classNames", "SwitchPrimitive", "extractProps", "marginPropDefs", "switchPropDefs", "Switch", "props", "forwardedRef", "className", "color", "radius", "switchProps"]}