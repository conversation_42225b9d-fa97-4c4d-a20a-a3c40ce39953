{"version": 3, "sources": ["../../../src/components/text-area.props.tsx"], "sourcesContent": ["import { colorPropDef } from '../props/color.prop.js';\nimport { radiusPropDef } from '../props/radius.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\nconst variants = ['classic', 'surface', 'soft'] as const;\nconst resizeValues = ['none', 'vertical', 'horizontal', 'both'] as const;\n\n// prettier-ignore\nconst textAreaPropDefs = {\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },\n  resize: { type: 'enum', className: 'rt-r-resize', values: resizeValues,  responsive: true },\n  ...colorPropDef,\n  ...radiusPropDef,\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  variant: PropDef<(typeof variants)[number]>;\n  resize: PropDef<(typeof resizeValues)[number]>;\n    };\n\nexport { textAreaPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,sBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA6B,kCAC7BC,EAA8B,mCAI9B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EACtBC,EAAW,CAAC,UAAW,UAAW,MAAM,EACxCC,EAAe,CAAC,OAAQ,WAAY,aAAc,MAAM,EAGxDN,EAAmB,CACvB,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQI,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQC,EAAU,QAAS,SAAU,EACvF,OAAQ,CAAE,KAAM,OAAQ,UAAW,cAAe,OAAQC,EAAe,WAAY,EAAK,EAC1F,GAAG,eACH,GAAG,eACL", "names": ["text_area_props_exports", "__export", "textAreaPropDefs", "__toCommonJS", "import_color_prop", "import_radius_prop", "sizes", "variants", "resize<PERSON><PERSON>ues"]}