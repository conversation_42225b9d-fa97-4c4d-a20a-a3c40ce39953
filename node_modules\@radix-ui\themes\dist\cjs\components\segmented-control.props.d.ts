declare const segmentedControlRootPropDefs: {
    radius: {
        type: "enum";
        values: readonly ["none", "small", "medium", "large", "full"];
        default: undefined;
    };
    disabled: {
        type: "boolean";
        className: string;
        default: false;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3"];
        default: "2";
        responsive: true;
    };
    variant: {
        type: "enum";
        className: string;
        values: readonly ["surface", "classic"];
        default: "surface";
    };
};
export { segmentedControlRootPropDefs };
//# sourceMappingURL=segmented-control.props.d.ts.map