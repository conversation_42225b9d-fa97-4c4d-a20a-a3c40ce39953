{"version": 3, "sources": ["../../../src/components/tabs.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\n\nconst tabsRootPropDefs = {\n  ...asChildPropDef,\n};\n\nconst tabsContentPropDefs = {\n  ...asChildPropDef,\n};\n\nexport { baseTabListPropDefs as tabsListPropDefs } from './_internal/base-tab-list.props.js';\nexport { tabsRootPropDefs, tabsContentPropDefs };\n"], "mappings": "AAAA,OAAS,kBAAAA,MAAsB,4BAE/B,MAAMC,EAAmB,CACvB,GAAGD,CACL,EAEME,EAAsB,CAC1B,GAAGF,CACL,EAEA,OAAgC,uBAAvBG,MAA+C", "names": ["asChildPropDef", "tabsRootPropDefs", "tabsContentPropDefs", "baseTabListPropDefs"]}