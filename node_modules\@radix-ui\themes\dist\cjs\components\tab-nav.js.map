{"version": 3, "sources": ["../../../src/components/tab-nav.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { NavigationMenu } from 'radix-ui';\n\nimport { tabNavRootPropDefs } from './tab-nav.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { getSubtree } from '../helpers/get-subtree.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { tabNavLinkPropDefs } from './tab-nav.props.js';\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TabNavRootElement = React.ElementRef<typeof NavigationMenu.Root>;\ntype TabNavRootElementProps = ComponentPropsWithout<'nav', RemovedProps>;\ntype TabNavOwnProps = GetPropDefTypes<typeof tabNavRootPropDefs>;\ninterface TabNavRootProps\n  extends Omit<TabNavRootElementProps, 'defaultValue' | 'dir' | 'color'>,\n    MarginProps,\n    TabNavOwnProps {}\nconst TabNavRoot = React.forwardRef<TabNavRootElement, TabNavRootProps>((props, forwardedRef) => {\n  const { children, className, color, ...rootProps } = extractProps(\n    props,\n    tabNavRootPropDefs,\n    marginPropDefs\n  );\n  return (\n    <NavigationMenu.Root\n      className=\"rt-TabNavRoot\"\n      data-accent-color={color}\n      {...rootProps}\n      asChild={false}\n      ref={forwardedRef}\n    >\n      <NavigationMenu.List\n        className={classNames('rt-reset', 'rt-BaseTabList', 'rt-TabNavList', className)}\n      >\n        {children}\n      </NavigationMenu.List>\n    </NavigationMenu.Root>\n  );\n});\nTabNavRoot.displayName = 'TabNav.Root';\n\ntype TabNavLinkElement = React.ElementRef<typeof NavigationMenu.Link>;\ntype TabNavLinkOwnProps = GetPropDefTypes<typeof tabNavLinkPropDefs>;\ninterface TabNavLinkProps\n  extends ComponentPropsWithout<typeof NavigationMenu.Link, RemovedProps | 'onSelect'>,\n    TabNavLinkOwnProps {}\nconst TabNavLink = React.forwardRef<TabNavLinkElement, TabNavLinkProps>((props, forwardedRef) => {\n  const { asChild, children, className, ...linkProps } = props;\n\n  return (\n    <NavigationMenu.Item className=\"rt-TabNavItem\">\n      <NavigationMenu.Link\n        {...linkProps}\n        ref={forwardedRef}\n        className={classNames('rt-reset', 'rt-BaseTabListTrigger', 'rt-TabNavLink', className)}\n        onSelect={undefined}\n        asChild={asChild}\n      >\n        {getSubtree({ asChild, children }, (children) => (\n          <>\n            <span className=\"rt-BaseTabListTriggerInner rt-TabNavLinkInner\">{children}</span>\n            <span className=\"rt-BaseTabListTriggerInnerHidden rt-TabNavLinkInnerHidden\">\n              {children}\n            </span>\n          </>\n        ))}\n      </NavigationMenu.Link>\n    </NavigationMenu.Item>\n  );\n});\nTabNavLink.displayName = 'TabNav.Link';\n\nexport { TabNavRoot as Root, TabNavLink as Link };\nexport type { TabNavRootProps as RootProps, TabNavLinkProps as LinkProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,UAAAE,EAAA,SAAAC,IAAA,eAAAC,EAAAJ,GAAA,IAAAK,EAAuB,oBACvBC,EAAuB,yBACvBC,EAA+B,oBAE/BC,EAAmC,8BACnCC,EAA6B,uCAC7BC,EAA2B,qCAC3BC,EAA+B,oCAc/B,MAAMR,EAAaE,EAAM,WAA+C,CAACO,EAAOC,IAAiB,CAC/F,KAAM,CAAE,SAAAC,EAAU,UAAAC,EAAW,MAAAC,EAAO,GAAGC,CAAU,KAAI,gBACnDL,EACA,qBACA,gBACF,EACA,OACEP,EAAA,cAAC,iBAAe,KAAf,CACC,UAAU,gBACV,oBAAmBW,EAClB,GAAGC,EACJ,QAAS,GACT,IAAKJ,GAELR,EAAA,cAAC,iBAAe,KAAf,CACC,aAAW,EAAAa,SAAW,WAAY,iBAAkB,gBAAiBH,CAAS,GAE7ED,CACH,CACF,CAEJ,CAAC,EACDX,EAAW,YAAc,cAOzB,MAAMD,EAAaG,EAAM,WAA+C,CAACO,EAAOC,IAAiB,CAC/F,KAAM,CAAE,QAAAM,EAAS,SAAAL,EAAU,UAAAC,EAAW,GAAGK,CAAU,EAAIR,EAEvD,OACEP,EAAA,cAAC,iBAAe,KAAf,CAAoB,UAAU,iBAC7BA,EAAA,cAAC,iBAAe,KAAf,CACE,GAAGe,EACJ,IAAKP,EACL,aAAW,EAAAK,SAAW,WAAY,wBAAyB,gBAAiBH,CAAS,EACrF,SAAU,OACV,QAASI,MAER,cAAW,CAAE,QAAAA,EAAS,SAAAL,CAAS,EAAIA,GAClCT,EAAA,cAAAA,EAAA,cACEA,EAAA,cAAC,QAAK,UAAU,iDAAiDS,CAAS,EAC1ET,EAAA,cAAC,QAAK,UAAU,6DACbS,CACH,CACF,CACD,CACH,CACF,CAEJ,CAAC,EACDZ,EAAW,YAAc", "names": ["tab_nav_exports", "__export", "TabNavLink", "TabNavRoot", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_tab_nav_props", "import_extract_props", "import_get_subtree", "import_margin_props", "props", "forwardedRef", "children", "className", "color", "rootProps", "classNames", "<PERSON><PERSON><PERSON><PERSON>", "linkProps"]}