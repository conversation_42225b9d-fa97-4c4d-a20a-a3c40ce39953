import { Portal as PortalPrimitive } from 'radix-ui';
export declare const Portal: import("react").ForwardRefExoticComponent<PortalPrimitive.PortalProps & import("react").RefAttributes<HTMLDivElement>>;
export declare const Root: import("react").ForwardRefExoticComponent<PortalPrimitive.PortalProps & import("react").RefAttributes<HTMLDivElement>>;
export type PortalProps = PortalPrimitive.PortalProps;
//# sourceMappingURL=portal.d.ts.map