import * as React from 'react';
import { quotePropDefs } from './quote.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type QuoteOwnProps = GetPropDefTypes<typeof quotePropDefs>;
interface QuoteProps extends ComponentPropsWithout<'q', RemovedProps>, QuoteOwnProps {
}
declare const Quote: React.ForwardRefExoticComponent<QuoteProps & React.RefAttributes<HTMLQuoteElement>>;
export { Quote };
export type { QuoteProps };
//# sourceMappingURL=quote.d.ts.map