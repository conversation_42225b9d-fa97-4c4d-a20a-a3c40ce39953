import { asChildPropDef } from '../props/as-child.prop.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const gridPropDefs: {
    gap: {
        type: "enum | string";
        className: string;
        customProperties: "--gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    gapX: {
        type: "enum | string";
        className: string;
        customProperties: "--column-gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    gapY: {
        type: "enum | string";
        className: string;
        customProperties: "--row-gap"[];
        values: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    /**
     * Sets the CSS **display** property.
     * Supports a subset of the corresponding CSS values and responsive objects.
     *
     * @example
     * display="inline-grid"
     * display={{ sm: 'none', lg: 'grid' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/display
     */
    display: {
        type: "enum";
        className: string;
        values: readonly ["none", "inline-grid", "grid"];
        responsive: true;
    };
    /**
     * Sets the CSS **grid-template** property.
     * Supports a subset of the corresponding CSS values and responsive objects.
     *
     * @example
     * template='"header header" "sidebar content"'
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-areas
     */
    areas: {
        type: "string";
        className: string;
        customProperties: "--grid-template-areas"[];
        responsive: true;
    };
    /**
     * Sets the CSS **grid-template-columns** property.
     * Supports numeric string values, CSS strings and responsive objects.
     *
     * Use numeric string values to create grid columns of even size.
     *
     * @example
     * columns="3"
     * columns="100px 1fr"
     * columns={{ xs: '1', md: 'auto 1fr' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-columns
     */
    columns: {
        type: "enum | string";
        className: string;
        customProperties: "--grid-template-columns"[];
        values: readonly ["1", "2", "3", "4", "5", "6", "7", "8", "9"];
        parseValue: typeof parseGridValue;
        responsive: true;
    };
    /**
     * Sets the CSS **grid-template-rows** property.
     * Supports numeric string values, CSS strings and responsive objects.
     *
     * Use numeric string values to create grid rows of even size.
     *
     * @example
     * rows="3"
     * rows="100px 1fr"
     * rows={{ xs: '1', md: 'auto 1fr' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-rows
     */
    rows: {
        type: "enum | string";
        className: string;
        customProperties: "--grid-template-rows"[];
        values: readonly ["1", "2", "3", "4", "5", "6", "7", "8", "9"];
        parseValue: typeof parseGridValue;
        responsive: true;
    };
    /**
     * Sets the CSS **grid-auto-flow** property.
     * Supports the corresponding CSS values and responsive objects.
     *
     * @example
     * flow="column"
     * flow={{ sm: 'column', lg: 'row' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/grid-auto-flow
     */
    flow: {
        type: "enum";
        className: string;
        values: readonly ["row", "column", "dense", "row-dense", "column-dense"];
        responsive: true;
    };
    /**
     * Sets the CSS **align-items** property.
     * Supports the corresponding CSS values and responsive objects.
     *
     * @example
     * align="center"
     * align={{ sm: 'baseline', lg: 'center' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/align-items
     */
    align: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end", "baseline", "stretch"];
        responsive: true;
    };
    /**
     * Sets the CSS **justify-content** property.
     * Supports a subset of the corresponding CSS values and responsive objects.
     *
     * @example
     * justify="between"
     * justify={{ sm: 'start', lg: 'center' }}
     *
     * @link
     * https://developer.mozilla.org/en-US/docs/Web/CSS/justify-content
     */
    justify: {
        type: "enum";
        className: string;
        values: readonly ["start", "center", "end", "between"];
        parseValue: typeof parseJustifyValue;
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
    /**
     * Controls whether to render **div** or **span**
     *
     * @example
     * as="div"
     * as="span"
     */
    as: {
        type: "enum";
        values: readonly ["div", "span"];
        default: "div";
    };
};
declare function parseGridValue(value: string): string;
declare function parseJustifyValue(value: string): string;
type GridOwnProps = GetPropDefTypes<typeof gridPropDefs & typeof asChildPropDef>;
export { gridPropDefs };
export type { GridOwnProps };
//# sourceMappingURL=grid.props.d.ts.map