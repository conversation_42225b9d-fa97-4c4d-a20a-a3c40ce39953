{"version": 3, "sources": ["../../../src/components/theme.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\nimport { accentColors, grayColors } from '../props/color.prop.js';\nimport { radii } from '../props/radius.prop.js';\n\nimport type { GetPropDefTypes, PropDef } from '../props/prop-def.js';\n\nconst appearances = ['inherit', 'light', 'dark'] as const;\nconst panelBackgrounds = ['solid', 'translucent'] as const;\nconst scalings = ['90%', '95%', '100%', '105%', '110%'] as const;\n\nconst themePropDefs = {\n  ...asChildPropDef,\n  /**\n   * Whether to apply background color to the Theme element.\n   *\n   * Defaults to true for the root Theme and for Theme elements that\n   * have an explicit light or dark appearance prop.\n   */\n  hasBackground: { type: 'boolean', default: true },\n  /**\n   * Sets the color scheme of the theme, typcially referred to as light and dark mode.\n   *\n   * @link\n   * https://www.radix-ui.com/themes/docs/theme/dark-mode\n   */\n  appearance: { type: 'enum', values: appearances, default: 'inherit' },\n  /**\n   * Selects one of the accent color options to use in the Theme.\n   *\n   * @link\n   * https://www.radix-ui.com/themes/docs/theme/color\n   */\n  accentColor: { type: 'enum', values: accentColors, default: 'indigo' },\n  /**\n   * Selects one of the gray color options to use in the Theme.\n   *\n   * @link\n   * https://www.radix-ui.com/themes/docs/theme/color\n   */\n  grayColor: { type: 'enum', values: grayColors, default: 'auto' },\n  /**\n   * Controls whether to use a solid or translucent background color on panelled\n   * elements such as Card or Table is solid or translucent.\n   *\n   * @link\n   * https://www.radix-ui.com/themes/docs/theme/visual-style\n   */\n  panelBackground: { type: 'enum', values: panelBackgrounds, default: 'translucent' },\n  /**\n   * Sets the default radius of the components.\n   *\n   * @link\n   * https://www.radix-ui.com/themes/docs/theme/visual-style\n   */\n  radius: { type: 'enum', values: radii, default: 'medium' },\n  /**\n   * Sets a scaling multiplier for values like spacing, font sizes, line heights, etc. are scaled.\n   *\n   * @link\n   * https://www.radix-ui.com/themes/docs/theme/layout\n   */\n  scaling: { type: 'enum', values: scalings, default: '100%' },\n} satisfies {\n  hasBackground: PropDef<boolean>;\n  appearance: PropDef<(typeof appearances)[number]>;\n  accentColor: PropDef<(typeof accentColors)[number]>;\n  grayColor: PropDef<(typeof grayColors)[number]>;\n  panelBackground: PropDef<(typeof panelBackgrounds)[number]>;\n  radius: PropDef<(typeof radii)[number]>;\n  scaling: PropDef<(typeof scalings)[number]>;\n};\n\ntype ThemeOwnProps = GetPropDefTypes<typeof themePropDefs & typeof asChildPropDef>;\n\nexport { themePropDefs };\nexport type { ThemeOwnProps };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,mBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA+B,qCAC/BC,EAAyC,kCACzCC,EAAsB,mCAItB,MAAMC,EAAc,CAAC,UAAW,QAAS,MAAM,EACzCC,EAAmB,CAAC,QAAS,aAAa,EAC1CC,EAAW,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAM,EAEhDP,EAAgB,CACpB,GAAG,iBAOH,cAAe,CAAE,KAAM,UAAW,QAAS,EAAK,EAOhD,WAAY,CAAE,KAAM,OAAQ,OAAQK,EAAa,QAAS,SAAU,EAOpE,YAAa,CAAE,KAAM,OAAQ,OAAQ,eAAc,QAAS,QAAS,EAOrE,UAAW,CAAE,KAAM,OAAQ,OAAQ,aAAY,QAAS,MAAO,EAQ/D,gBAAiB,CAAE,KAAM,OAAQ,OAAQC,EAAkB,QAAS,aAAc,EAOlF,OAAQ,CAAE,KAAM,OAAQ,OAAQ,QAAO,QAAS,QAAS,EAOzD,QAAS,CAAE,KAAM,OAAQ,OAAQC,EAAU,QAAS,MAAO,CAC7D", "names": ["theme_props_exports", "__export", "themePropDefs", "__toCommonJS", "import_as_child_prop", "import_color_prop", "import_radius_prop", "appearances", "panelBackgrounds", "scalings"]}