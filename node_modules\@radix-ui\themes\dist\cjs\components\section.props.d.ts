import { asChildPropDef } from '../props/as-child.prop.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const sectionPropDefs: {
    /**
     * Controls the vertical padding of the section.
     *
     * @values
     * | Size     | Padding |
     * | :------- | ------: |
     * | size="1" | 24px    |
     * | size="2" | 40px    |
     * | size="3" | 64px    |
     * | size="4" | 80px    |
     *
     * @example
     * size="4"
     * size={{ sm: '3', lg: '4' }}
     *
     * @link
     * https://github.com/radix-ui/themes/blob/main/packages/radix-ui-themes/src/components/section.css
     */
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4"];
        default: "3";
        responsive: true;
    };
    /**
     * Controls whether the section is visible or hidden.
     * Supports "none", "initial", and responsive object values.
     *
     * @example
     * display="none"
     * display={{ sm: 'none', lg: 'initial' }}
     */
    display: {
        type: "enum";
        className: string;
        values: readonly ["none", "initial"];
        parseValue: typeof parseDisplayValue;
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
};
declare function parseDisplayValue(value: string): string;
type SectionOwnProps = GetPropDefTypes<typeof sectionPropDefs & typeof asChildPropDef>;
export { sectionPropDefs };
export type { SectionOwnProps };
//# sourceMappingURL=section.props.d.ts.map