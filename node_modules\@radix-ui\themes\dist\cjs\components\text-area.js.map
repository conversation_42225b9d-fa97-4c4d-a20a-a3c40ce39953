{"version": 3, "sources": ["../../../src/components/text-area.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\n\nimport { textAreaPropDefs } from './text-area.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TextAreaElement = React.ElementRef<'textarea'>;\ntype TextAreaOwnProps = GetPropDefTypes<typeof textAreaPropDefs> & {\n  defaultValue?: string;\n  value?: string;\n};\ninterface TextAreaProps\n  extends ComponentPropsWithout<'textarea', RemovedProps | 'size' | 'value'>,\n    MarginProps,\n    TextAreaOwnProps {}\nconst TextArea = React.forwardRef<TextAreaElement, TextAreaProps>((props, forwardedRef) => {\n  const { className, color, radius, style, ...textAreaProps } = extractProps(\n    props,\n    textAreaPropDefs,\n    marginPropDefs\n  );\n  return (\n    <div\n      data-accent-color={color}\n      data-radius={radius}\n      className={classNames('rt-TextAreaRoot', className)}\n      style={style}\n    >\n      <textarea className=\"rt-reset rt-TextAreaInput\" ref={forwardedRef} {...textAreaProps} />\n    </div>\n  );\n});\nTextArea.displayName = 'TextArea';\n\nexport { TextArea };\nexport type { TextAreaProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,cAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,oBACvBC,EAAuB,yBAEvBC,EAAiC,gCACjCC,EAA6B,uCAC7BC,EAA+B,oCAe/B,MAAMN,EAAWE,EAAM,WAA2C,CAACK,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAC,EAAW,MAAAC,EAAO,OAAAC,EAAQ,MAAAC,EAAO,GAAGC,CAAc,KAAI,gBAC5DN,EACA,mBACA,gBACF,EACA,OACEL,EAAA,cAAC,OACC,oBAAmBQ,EACnB,cAAaC,EACb,aAAW,EAAAG,SAAW,kBAAmBL,CAAS,EAClD,MAAOG,GAEPV,EAAA,cAAC,YAAS,UAAU,4BAA4B,IAAKM,EAAe,GAAGK,EAAe,CACxF,CAEJ,CAAC,EACDb,EAAS,YAAc", "names": ["text_area_exports", "__export", "TextArea", "__toCommonJS", "React", "import_classnames", "import_text_area_props", "import_extract_props", "import_margin_props", "props", "forwardedRef", "className", "color", "radius", "style", "textAreaProps", "classNames"]}