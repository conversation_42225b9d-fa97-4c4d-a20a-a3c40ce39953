{"version": 3, "sources": ["../../../src/components/text.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\nimport { colorPropDef } from '../props/color.prop.js';\nimport { highContrastPropDef } from '../props/high-contrast.prop.js';\nimport { leadingTrimPropDef } from '../props/leading-trim.prop.js';\nimport { textAlignPropDef } from '../props/text-align.prop.js';\nimport { textWrapPropDef } from '../props/text-wrap.prop.js';\nimport { truncatePropDef } from '../props/truncate.prop.js';\nimport { weightPropDef } from '../props/weight.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst as = ['span', 'div', 'label', 'p'] as const;\nconst sizes = ['1', '2', '3', '4', '5', '6', '7', '8', '9'] as const;\n\nconst textPropDefs = {\n  as: { type: 'enum', values: as, default: 'span' },\n  ...asChildPropDef,\n  size: {\n    type: 'enum',\n    className: 'rt-r-size',\n    values: sizes,\n    responsive: true,\n  },\n  ...weightPropDef,\n  ...textAlignPropDef,\n  ...leadingTrimPropDef,\n  ...truncatePropDef,\n  ...textWrapPropDef,\n  ...colorPropDef,\n  ...highContrastPropDef,\n} satisfies {\n  as: PropDef<(typeof as)[number]>;\n  size: PropDef<(typeof sizes)[number]>;\n};\n\nexport { textPropDefs };\n"], "mappings": "AAAA,OAAS,kBAAAA,MAAsB,4BAC/B,OAAS,gBAAAC,MAAoB,yBAC7B,OAAS,uBAAAC,MAA2B,iCACpC,OAAS,sBAAAC,MAA0B,gCACnC,OAAS,oBAAAC,MAAwB,8BACjC,OAAS,mBAAAC,MAAuB,6BAChC,OAAS,mBAAAC,MAAuB,4BAChC,OAAS,iBAAAC,MAAqB,0BAI9B,MAAMC,EAAK,CAAC,OAAQ,MAAO,QAAS,GAAG,EACjCC,EAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAEpDC,EAAe,CACnB,GAAI,CAAE,KAAM,OAAQ,OAAQF,EAAI,QAAS,MAAO,EAChD,GAAGR,EACH,KAAM,CACJ,KAAM,OACN,UAAW,YACX,OAAQS,EACR,WAAY,EACd,EACA,GAAGF,EACH,GAAGH,EACH,GAAGD,EACH,GAAGG,EACH,GAAGD,EACH,GAAGJ,EACH,GAAGC,CACL", "names": ["asChildPropDef", "colorPropDef", "highContrastPropDef", "leadingTrimPropDef", "textAlignPropDef", "textWrapPropDef", "truncatePropDef", "weightPropDef", "as", "sizes", "textPropDefs"]}