{"version": 3, "sources": ["../../../src/components/text-field.props.tsx"], "sourcesContent": ["import { colorPropDef } from '../props/color.prop.js';\nimport { paddingPropDefs } from '../props/padding.props.js';\nimport { radiusPropDef } from '../props/radius.prop.js';\nimport { flexPropDefs } from './flex.props.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\nconst variants = ['classic', 'surface', 'soft'] as const;\n\nconst textFieldRootPropDefs = {\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },\n  ...colorPropDef,\n  ...radiusPropDef,\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  variant: PropDef<(typeof variants)[number]>;\n};\n\nconst sides = ['left', 'right'] as const;\n\nconst textFieldSlotPropDefs = {\n  side: { type: 'enum', values: sides },\n  ...colorPropDef,\n  gap: flexPropDefs.gap,\n  px: paddingPropDefs.px,\n  pl: paddingPropDefs.pl,\n  pr: paddingPropDefs.pr,\n} satisfies {\n  side: PropDef<(typeof sides)[number]>;\n  gap: typeof flexPropDefs.gap;\n  px: typeof paddingPropDefs.px;\n  pl: typeof paddingPropDefs.pl;\n  pr: typeof paddingPropDefs.pr;\n};\n\nexport { textFieldRootPropDefs, textFieldSlotPropDefs };\n"], "mappings": "AAAA,OAAS,gBAAAA,MAAoB,yBAC7B,OAAS,mBAAAC,MAAuB,4BAChC,OAAS,iBAAAC,MAAqB,0BAC9B,OAAS,gBAAAC,MAAoB,kBAI7B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EACtBC,EAAW,CAAC,UAAW,UAAW,MAAM,EAExCC,EAAwB,CAC5B,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQF,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQC,EAAU,QAAS,SAAU,EACvF,GAAGL,EACH,GAAGE,CACL,EAKMK,EAAQ,CAAC,OAAQ,OAAO,EAExBC,EAAwB,CAC5B,KAAM,CAAE,KAAM,OAAQ,OAAQD,CAAM,EACpC,GAAGP,EACH,IAAKG,EAAa,IAClB,GAAIF,EAAgB,GACpB,GAAIA,EAAgB,GACpB,GAAIA,EAAgB,EACtB", "names": ["colorPropDef", "paddingPropDefs", "radiusPropDef", "flexPropDefs", "sizes", "variants", "textFieldRootPropDefs", "sides", "textFieldSlotPropDefs"]}