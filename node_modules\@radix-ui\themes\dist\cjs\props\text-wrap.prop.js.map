{"version": 3, "sources": ["../../../src/props/text-wrap.prop.ts"], "sourcesContent": ["import type { PropDef } from './prop-def.js';\n\nconst textWrapValues = ['wrap', 'nowrap', 'pretty', 'balance'] as const;\n\nconst textWrapPropDef = {\n  wrap: {\n    type: 'enum',\n    className: 'rt-r-tw',\n    values: textWrapValues,\n    responsive: true,\n  },\n} satisfies {\n  wrap: PropDef<(typeof textWrapValues)[number]>;\n};\n\nexport { textWrapPropDef };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,qBAAAE,IAAA,eAAAC,EAAAH,GAEA,MAAMI,EAAiB,CAAC,OAAQ,SAAU,SAAU,SAAS,EAEvDF,EAAkB,CACtB,KAAM,CACJ,KAAM,OACN,UAAW,UACX,OAAQE,EACR,WAAY,EACd,CACF", "names": ["text_wrap_prop_exports", "__export", "textWrapPropDef", "__toCommonJS", "textWrapValues"]}