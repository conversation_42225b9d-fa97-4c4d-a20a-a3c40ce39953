declare const linkPropDefs: {
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: "gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky";
    };
    underline: {
        type: "enum";
        className: string;
        values: readonly ["auto", "always", "hover", "none"];
        default: "auto";
    };
    wrap: {
        type: "enum";
        className: string;
        values: readonly ["wrap", "nowrap", "pretty", "balance"];
        responsive: true;
    };
    truncate: {
        type: "boolean";
        className: string;
    };
    trim: {
        type: "enum";
        className: string;
        values: readonly ["normal", "start", "end", "both"];
        responsive: true;
    };
    weight: {
        type: "enum";
        className: string;
        values: readonly ["light", "regular", "medium", "bold"];
        responsive: true;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4", "5", "6", "7", "8", "9"];
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
};
export { linkPropDefs };
//# sourceMappingURL=link.props.d.ts.map