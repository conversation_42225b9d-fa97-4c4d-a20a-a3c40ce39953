'use client';

import React from 'react';
import { Flex, Spinner, Text } from '@radix-ui/themes';
import type { LoadingStateProps } from '@/types';

export function LoadingState({
  message = "Loading...",
  className = '',
}: LoadingStateProps): React.ReactElement {
  return (
    <Flex align="center" gap="2" className={className}>
      <Spinner />
      <Text color="gray">{message}</Text>
    </Flex>
  );
}
