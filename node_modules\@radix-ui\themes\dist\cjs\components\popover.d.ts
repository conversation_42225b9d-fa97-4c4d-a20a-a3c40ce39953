import * as React from 'react';
import { Popover as PopoverPrimitive } from 'radix-ui';
import type { PopoverContentOwnProps } from './popover.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface PopoverRootProps extends React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Root> {
}
declare const PopoverRoot: React.FC<PopoverRootProps>;
interface PopoverTriggerProps extends ComponentPropsWithout<typeof PopoverPrimitive.Trigger, RemovedProps> {
}
declare const PopoverTrigger: React.ForwardRefExoticComponent<PopoverTriggerProps & React.RefAttributes<HTMLButtonElement>>;
interface PopoverContentProps extends ComponentPropsWithout<typeof PopoverPrimitive.Content, RemovedProps>, PopoverContentOwnProps {
    container?: React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Portal>['container'];
}
declare const PopoverContent: React.ForwardRefExoticComponent<PopoverContentProps & React.RefAttributes<HTMLDivElement>>;
interface PopoverCloseProps extends ComponentPropsWithout<typeof PopoverPrimitive.Close, RemovedProps> {
}
declare const PopoverClose: React.ForwardRefExoticComponent<PopoverCloseProps & React.RefAttributes<HTMLButtonElement>>;
interface PopoverAnchorProps extends React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Anchor> {
}
declare const PopoverAnchor: React.ForwardRefExoticComponent<PopoverAnchorProps & React.RefAttributes<HTMLDivElement>>;
export { PopoverRoot as Root, PopoverContent as Content, PopoverTrigger as Trigger, PopoverClose as Close, PopoverAnchor as Anchor, };
export type { PopoverRootProps as RootProps, PopoverContentProps as ContentProps, PopoverTriggerProps as TriggerProps, PopoverCloseProps as CloseProps, PopoverAnchorProps as AnchorProps, };
//# sourceMappingURL=popover.d.ts.map