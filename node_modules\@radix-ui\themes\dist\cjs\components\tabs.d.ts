import * as React from 'react';
import { Tabs as TabsPrimitive } from 'radix-ui';
import { tabsListPropDefs } from './tabs.props.js';
import type { tabsContentPropDefs, tabsRootPropDefs } from './tabs.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type TabsRootOwnProps = GetPropDefTypes<typeof tabsRootPropDefs>;
interface TabsRootProps extends ComponentPropsWithout<typeof TabsPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>, MarginProps, TabsRootOwnProps {
}
declare const TabsRoot: React.ForwardRefExoticComponent<TabsRootProps & React.RefAttributes<HTMLDivElement>>;
type TabsListOwnProps = GetPropDefTypes<typeof tabsListPropDefs>;
interface TabsListProps extends ComponentPropsWithout<typeof TabsPrimitive.List, RemovedProps>, MarginProps, TabsListOwnProps {
}
declare const TabsList: React.ForwardRefExoticComponent<TabsListProps & React.RefAttributes<HTMLDivElement>>;
interface TabsTriggerProps extends ComponentPropsWithout<typeof TabsPrimitive.Trigger, RemovedProps> {
}
declare const TabsTrigger: React.ForwardRefExoticComponent<TabsTriggerProps & React.RefAttributes<HTMLButtonElement>>;
type TabsContentOwnProps = GetPropDefTypes<typeof tabsContentPropDefs>;
interface TabsContentProps extends ComponentPropsWithout<typeof TabsPrimitive.Content, RemovedProps>, MarginProps, TabsContentOwnProps {
}
declare const TabsContent: React.ForwardRefExoticComponent<TabsContentProps & React.RefAttributes<HTMLDivElement>>;
export { TabsRoot as Root, TabsList as List, TabsTrigger as Trigger, TabsContent as Content };
export type { TabsRootProps as RootProps, TabsListProps as ListProps, TabsTriggerProps as TriggerProps, TabsContentProps as ContentProps, };
//# sourceMappingURL=tabs.d.ts.map