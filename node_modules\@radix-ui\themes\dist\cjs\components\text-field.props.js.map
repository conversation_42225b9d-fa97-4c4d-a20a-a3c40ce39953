{"version": 3, "sources": ["../../../src/components/text-field.props.tsx"], "sourcesContent": ["import { colorPropDef } from '../props/color.prop.js';\nimport { paddingPropDefs } from '../props/padding.props.js';\nimport { radiusPropDef } from '../props/radius.prop.js';\nimport { flexPropDefs } from './flex.props.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\nconst variants = ['classic', 'surface', 'soft'] as const;\n\nconst textFieldRootPropDefs = {\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },\n  ...colorPropDef,\n  ...radiusPropDef,\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  variant: PropDef<(typeof variants)[number]>;\n};\n\nconst sides = ['left', 'right'] as const;\n\nconst textFieldSlotPropDefs = {\n  side: { type: 'enum', values: sides },\n  ...colorPropDef,\n  gap: flexPropDefs.gap,\n  px: paddingPropDefs.px,\n  pl: paddingPropDefs.pl,\n  pr: paddingPropDefs.pr,\n} satisfies {\n  side: PropDef<(typeof sides)[number]>;\n  gap: typeof flexPropDefs.gap;\n  px: typeof paddingPropDefs.px;\n  pl: typeof paddingPropDefs.pl;\n  pr: typeof paddingPropDefs.pr;\n};\n\nexport { textFieldRootPropDefs, textFieldSlotPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,2BAAAE,EAAA,0BAAAC,IAAA,eAAAC,EAAAJ,GAAA,IAAAK,EAA6B,kCAC7BC,EAAgC,qCAChCC,EAA8B,mCAC9BC,EAA6B,2BAI7B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EACtBC,EAAW,CAAC,UAAW,UAAW,MAAM,EAExCR,EAAwB,CAC5B,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQO,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQC,EAAU,QAAS,SAAU,EACvF,GAAG,eACH,GAAG,eACL,EAKMC,EAAQ,CAAC,OAAQ,OAAO,EAExBR,EAAwB,CAC5B,KAAM,CAAE,KAAM,OAAQ,OAAQQ,CAAM,EACpC,GAAG,eACH,IAAK,eAAa,IAClB,GAAI,kBAAgB,GACpB,GAAI,kBAAgB,GACpB,GAAI,kBAAgB,EACtB", "names": ["text_field_props_exports", "__export", "textFieldRootPropDefs", "textFieldSlotPropDefs", "__toCommonJS", "import_color_prop", "import_padding_props", "import_radius_prop", "import_flex_props", "sizes", "variants", "sides"]}