'use client';

import React from 'react';
import { Box, Flex, Text, SegmentedControl } from '@radix-ui/themes';
import type { UnitsSelectorProps, Units } from '@/types';

export function UnitsSelector({
  units,
  onUnitsChange,
  className = '',
}: UnitsSelectorProps): React.ReactElement {
  const handleTemperatureChange = (value: string) => {
    const newUnits: Units = {
      ...units,
      temp: value as 'celsius' | 'fahrenheit',
    };
    onUnitsChange(newUnits);
  };

  const handleWindChange = (value: string) => {
    const newUnits: Units = {
      ...units,
      wind: value as 'kmh' | 'mph' | 'ms' | 'kn',
    };
    onUnitsChange(newUnits);
  };

  return (
    <Box mt="4" className={className}>
      <Flex align="center" justify="between" wrap="wrap" gap="3">
        <Text size="2" color="gray">
          Units
        </Text>
        <Flex align="center" gap="3" wrap="wrap">
          <SegmentedControl.Root 
            value={units.temp} 
            onValueChange={handleTemperatureChange}
          >
            <SegmentedControl.Item value="celsius">
              °C
            </SegmentedControl.Item>
            <SegmentedControl.Item value="fahrenheit">
              °F
            </SegmentedControl.Item>
          </SegmentedControl.Root>
          
          <SegmentedControl.Root 
            value={units.wind} 
            onValueChange={handleWindChange}
          >
            <SegmentedControl.Item value="kmh">
              km/h
            </SegmentedControl.Item>
            <SegmentedControl.Item value="mph">
              mph
            </SegmentedControl.Item>
          </SegmentedControl.Root>
        </Flex>
      </Flex>
    </Box>
  );
}
