{"version": 3, "sources": ["../../../src/components/tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Tabs as TabsPrimitive } from 'radix-ui';\n\nimport { tabsListPropDefs } from './tabs.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { tabsContentPropDefs, tabsRootPropDefs } from './tabs.props.js';\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TabsRootElement = React.ElementRef<typeof TabsPrimitive.Root>;\ntype TabsRootOwnProps = GetPropDefTypes<typeof tabsRootPropDefs>;\ninterface TabsRootProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>,\n    MarginProps,\n    TabsRootOwnProps {}\nconst TabsRoot = React.forwardRef<TabsRootElement, TabsRootProps>((props, forwardedRef) => {\n  const { className, ...rootProps } = extractProps(props, marginPropDefs);\n  return (\n    <TabsPrimitive.Root\n      {...rootProps}\n      ref={forwardedRef}\n      className={classNames('rt-TabsRoot', className)}\n    />\n  );\n});\nTabsRoot.displayName = 'Tabs.Root';\n\ntype TabsListElement = React.ElementRef<typeof TabsPrimitive.List>;\ntype TabsListOwnProps = GetPropDefTypes<typeof tabsListPropDefs>;\ninterface TabsListProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.List, RemovedProps>,\n    MarginProps,\n    TabsListOwnProps {}\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>((props, forwardedRef) => {\n  const { className, color, ...listProps } = extractProps(props, tabsListPropDefs, marginPropDefs);\n  return (\n    <TabsPrimitive.List\n      data-accent-color={color}\n      {...listProps}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-BaseTabList', 'rt-TabsList', className)}\n    />\n  );\n});\nTabsList.displayName = 'Tabs.List';\n\ntype TabsTriggerElement = React.ElementRef<typeof TabsPrimitive.Trigger>;\ninterface TabsTriggerProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.Trigger, RemovedProps> {}\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props, forwardedRef) => {\n    const { className, children, ...triggerProps } = props;\n    return (\n      <TabsPrimitive.Trigger\n        {...triggerProps}\n        asChild={false}\n        ref={forwardedRef}\n        className={classNames('rt-reset', 'rt-BaseTabListTrigger', 'rt-TabsTrigger', className)}\n      >\n        <span className=\"rt-BaseTabListTriggerInner rt-TabsTriggerInner\">{children}</span>\n        <span className=\"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden\">\n          {children}\n        </span>\n      </TabsPrimitive.Trigger>\n    );\n  }\n);\nTabsTrigger.displayName = 'Tabs.Trigger';\n\ntype TabsContentElement = React.ElementRef<typeof TabsPrimitive.Content>;\ntype TabsContentOwnProps = GetPropDefTypes<typeof tabsContentPropDefs>;\ninterface TabsContentProps\n  extends ComponentPropsWithout<typeof TabsPrimitive.Content, RemovedProps>,\n    MarginProps,\n    TabsContentOwnProps {}\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props, forwardedRef) => {\n    const { className, ...contentProps } = extractProps(props, marginPropDefs);\n    return (\n      <TabsPrimitive.Content\n        {...contentProps}\n        ref={forwardedRef}\n        className={classNames('rt-TabsContent', className)}\n      />\n    );\n  }\n);\nTabsContent.displayName = 'Tabs.Content';\n\nexport { TabsRoot as Root, TabsList as List, TabsTrigger as Trigger, TabsContent as Content };\nexport type {\n  TabsRootProps as RootProps,\n  TabsListProps as ListProps,\n  TabsTriggerProps as TriggerProps,\n  TabsContentProps as ContentProps,\n};\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,QAAQC,MAAqB,WAEtC,OAAS,oBAAAC,MAAwB,kBACjC,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,kBAAAC,MAAsB,2BAa/B,MAAMC,EAAWN,EAAM,WAA2C,CAACO,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAC,EAAW,GAAGC,CAAU,EAAIN,EAAaG,EAAOF,CAAc,EACtE,OACEL,EAAA,cAACE,EAAc,KAAd,CACE,GAAGQ,EACJ,IAAKF,EACL,UAAWP,EAAW,cAAeQ,CAAS,EAChD,CAEJ,CAAC,EACDH,EAAS,YAAc,YAQvB,MAAMK,EAAWX,EAAM,WAA2C,CAACO,EAAOC,IAAiB,CACzF,KAAM,CAAE,UAAAC,EAAW,MAAAG,EAAO,GAAGC,CAAU,EAAIT,EAAaG,EAAOJ,EAAkBE,CAAc,EAC/F,OACEL,EAAA,cAACE,EAAc,KAAd,CACC,oBAAmBU,EAClB,GAAGC,EACJ,QAAS,GACT,IAAKL,EACL,UAAWP,EAAW,iBAAkB,cAAeQ,CAAS,EAClE,CAEJ,CAAC,EACDE,EAAS,YAAc,YAKvB,MAAMG,EAAcd,EAAM,WACxB,CAACO,EAAOC,IAAiB,CACvB,KAAM,CAAE,UAAAC,EAAW,SAAAM,EAAU,GAAGC,CAAa,EAAIT,EACjD,OACEP,EAAA,cAACE,EAAc,QAAd,CACE,GAAGc,EACJ,QAAS,GACT,IAAKR,EACL,UAAWP,EAAW,WAAY,wBAAyB,iBAAkBQ,CAAS,GAEtFT,EAAA,cAAC,QAAK,UAAU,kDAAkDe,CAAS,EAC3Ef,EAAA,cAAC,QAAK,UAAU,8DACbe,CACH,CACF,CAEJ,CACF,EACAD,EAAY,YAAc,eAQ1B,MAAMG,EAAcjB,EAAM,WACxB,CAACO,EAAOC,IAAiB,CACvB,KAAM,CAAE,UAAAC,EAAW,GAAGS,CAAa,EAAId,EAAaG,EAAOF,CAAc,EACzE,OACEL,EAAA,cAACE,EAAc,QAAd,CACE,GAAGgB,EACJ,IAAKV,EACL,UAAWP,EAAW,iBAAkBQ,CAAS,EACnD,CAEJ,CACF,EACAQ,EAAY,YAAc", "names": ["React", "classNames", "TabsPrimitive", "tabsListPropDefs", "extractProps", "marginPropDefs", "TabsRoot", "props", "forwardedRef", "className", "rootProps", "TabsList", "color", "listProps", "TabsTrigger", "children", "triggerProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentProps"]}