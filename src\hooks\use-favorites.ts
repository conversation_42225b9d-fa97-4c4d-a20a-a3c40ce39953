'use client';

import { useCallback } from 'react';
import { useLocalStorage } from './use-local-storage';
import type { UseFavoritesReturn, Location } from '@/types';

const MAX_FAVORITES = 8;

export function useFavorites(): UseFavoritesReturn {
  const { value: favorites, setValue: setFavorites } = useLocalStorage<Location[]>('favs', []);

  const generateLocationKey = (location: Location): string => {
    return `${location.name}-${location.latitude}-${location.longitude}`;
  };

  const isFavorite = useCallback((location: Location): boolean => {
    const key = generateLocationKey(location);
    return favorites.some(fav => generateLocationKey(fav) === key);
  }, [favorites]);

  const addFavorite = useCallback((location: Location): void => {
    if (isFavorite(location)) {
      return; // Already a favorite
    }

    const newFavorites = [location, ...favorites].slice(0, MAX_FAVORITES);
    setFavorites(newFavorites);
  }, [favorites, isFavorite, setFavorites]);

  const removeFavorite = useCallback((location: Location): void => {
    const key = generateLocationKey(location);
    const newFavorites = favorites.filter(fav => generateLocationKey(fav) !== key);
    setFavorites(newFavorites);
  }, [favorites, setFavorites]);

  const toggleFavorite = useCallback((location: Location): void => {
    if (isFavorite(location)) {
      removeFavorite(location);
    } else {
      addFavorite(location);
    }
  }, [isFavorite, addFavorite, removeFavorite]);

  const clearFavorites = useCallback((): void => {
    setFavorites([]);
  }, [setFavorites]);

  return {
    favorites,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    isFavorite,
    clearFavorites,
  };
}
